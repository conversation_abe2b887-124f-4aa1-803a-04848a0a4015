#!/bin/bash

# 测试构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}开始测试 Docker 镜像构建...${NC}"

# 测试 Nginx 配置
echo -e "${BLUE}测试 Nginx 配置...${NC}"
if [ -f "scripts/nginx-config.sh" ]; then
    chmod +x scripts/nginx-config.sh
    if ./scripts/nginx-config.sh validate; then
        echo -e "${GREEN}✅ Nginx 配置验证成功${NC}"
    else
        echo -e "${YELLOW}⚠️ Nginx 配置验证失败，但继续构建${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ 未找到 nginx-config.sh 脚本${NC}"
fi

# 测试前端构建
echo -e "${BLUE}测试前端镜像构建...${NC}"
if docker-compose build frontend; then
    echo -e "${GREEN}✅ 前端镜像构建成功${NC}"
else
    echo -e "${RED}❌ 前端镜像构建失败${NC}"
    exit 1
fi

# 测试后端构建
echo -e "${BLUE}测试后端镜像构建...${NC}"
if docker-compose build backend; then
    echo -e "${GREEN}✅ 后端镜像构建成功${NC}"
else
    echo -e "${RED}❌ 后端镜像构建失败${NC}"
    exit 1
fi

echo -e "${GREEN}🎉 所有镜像构建成功！${NC}"

# 显示镜像信息
echo -e "${BLUE}构建的镜像信息:${NC}"
docker images | grep -E "(admin_pinan|dk_)"
