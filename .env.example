# admin_pinan 环境配置文件示例
# 复制此文件为 .env 并根据需要修改配置

# ==========================================
# 数据库配置
# ==========================================

# MySQL 配置
MYSQL_ROOT_PASSWORD=root123456
MYSQL_DATABASE=pinandai
MYSQL_USERNAME=pinan
MYSQL_PASSWORD=pinan123
MYSQL_HOST=mysql
MYSQL_PORT=3306

# ==========================================
# Redis 配置
# ==========================================

# Redis 配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DATABASE=2
REDIS_PASSWORD=

# ==========================================
# 应用配置
# ==========================================

# Spring 配置
SPRING_PROFILES_ACTIVE=docker

# 后端服务配置
BACKEND_PORT=8088
BACKEND_HOST=backend

# 前端服务配置
FRONTEND_PORT=80
FRONTEND_HOST=frontend

# API 基础 URL
API_BASE_URL=http://localhost:8088

# ==========================================
# 安全配置
# ==========================================

# JWT 配置
JWT_SECRET=kgsheuaogkvlruisrvvkhfzfju
JWT_EXPIRE_TIME=120

# Druid 监控配置
DRUID_USERNAME=admin
DRUID_PASSWORD=admin123

# ==========================================
# 文件上传配置
# ==========================================

# 上传路径
UPLOAD_PATH=/app/upload

# 最大文件大小 (MB)
MAX_FILE_SIZE=50

# ==========================================
# 日志配置
# ==========================================

# 日志级别
LOG_LEVEL=info

# 日志路径
LOG_PATH=/app/logs

# ==========================================
# 时区配置
# ==========================================

# 时区设置
TZ=Asia/Shanghai

# ==========================================
# Docker 配置
# ==========================================

# 容器名称前缀
COMPOSE_PROJECT_NAME=admin_pinan

# 网络配置
NETWORK_SUBNET=**********/16

# 数据卷配置
MYSQL_DATA_VOLUME=mysql_data
REDIS_DATA_VOLUME=redis_data
BACKEND_LOGS_VOLUME=backend_logs
NGINX_LOGS_VOLUME=nginx_logs

# ==========================================
# 开发配置
# ==========================================

# 开发模式
DEBUG=false

# 热重载
HOT_RELOAD=false

# Mock 数据
MOCK_ENABLED=false

# ==========================================
# Nginx 配置
# ==========================================

# Nginx 配置环境 (default|dev|prod)
NGINX_CONFIG_ENV=default

# 是否启用 Gzip 压缩
NGINX_GZIP_ENABLED=true

# 客户端最大请求体大小
NGINX_CLIENT_MAX_BODY_SIZE=50M

# ==========================================
# 监控配置
# ==========================================

# 健康检查间隔 (秒)
HEALTH_CHECK_INTERVAL=30

# 健康检查超时 (秒)
HEALTH_CHECK_TIMEOUT=10

# 健康检查重试次数
HEALTH_CHECK_RETRIES=3

# ==========================================
# 性能配置
# ==========================================

# JVM 内存配置
JVM_XMS=512m
JVM_XMX=1024m

# MySQL 连接池配置
DB_INITIAL_SIZE=5
DB_MIN_IDLE=10
DB_MAX_ACTIVE=20

# Redis 连接池配置
REDIS_MAX_ACTIVE=100
REDIS_MAX_IDLE=8
REDIS_MIN_IDLE=0
