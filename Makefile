# admin_pinan 金融贷款管理系统 Makefile
# 适用于 Windows Docker Desktop + WSL2 环境

.PHONY: help check build start stop restart logs clean backup restore

# 默认目标
.DEFAULT_GOAL := help

# 颜色定义
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
BLUE := \033[34m
NC := \033[0m

# 项目配置
PROJECT_NAME := admin_pinan
COMPOSE_FILE := docker-compose.yml

help: ## 显示帮助信息
	@echo "$(GREEN)admin_pinan 金融贷款管理系统$(NC)"
	@echo "$(BLUE)适用于 Windows Docker Desktop + WSL2 环境$(NC)"
	@echo ""
	@echo "$(YELLOW)可用命令:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(YELLOW)示例:$(NC)"
	@echo "  make check     # 检查环境"
	@echo "  make start     # 启动系统"
	@echo "  make logs      # 查看日志"
	@echo "  make stop      # 停止系统"

check: ## 检查环境和依赖
	@echo "$(BLUE)检查环境...$(NC)"
	@chmod +x scripts/*.sh
	@./scripts/check-env.sh

build: ## 构建 Docker 镜像
	@echo "$(BLUE)构建 Docker 镜像...$(NC)"
	@docker-compose build --no-cache

start: ## 启动所有服务
	@echo "$(BLUE)启动 admin_pinan 系统...$(NC)"
	@chmod +x scripts/*.sh
	@./scripts/start.sh

stop: ## 停止所有服务
	@echo "$(BLUE)停止 admin_pinan 系统...$(NC)"
	@./scripts/stop.sh

restart: ## 重启所有服务
	@echo "$(BLUE)重启 admin_pinan 系统...$(NC)"
	@docker-compose restart

logs: ## 查看所有服务日志
	@echo "$(BLUE)查看服务日志...$(NC)"
	@./scripts/logs.sh all -f

logs-backend: ## 查看后端服务日志
	@echo "$(BLUE)查看后端服务日志...$(NC)"
	@./scripts/logs.sh backend -f

logs-frontend: ## 查看前端服务日志
	@echo "$(BLUE)查看前端服务日志...$(NC)"
	@./scripts/logs.sh frontend -f

logs-mysql: ## 查看 MySQL 服务日志
	@echo "$(BLUE)查看 MySQL 服务日志...$(NC)"
	@./scripts/logs.sh mysql -f

logs-redis: ## 查看 Redis 服务日志
	@echo "$(BLUE)查看 Redis 服务日志...$(NC)"
	@./scripts/logs.sh redis -f

status: ## 查看服务状态
	@echo "$(BLUE)查看服务状态...$(NC)"
	@docker-compose ps

shell-backend: ## 进入后端容器
	@echo "$(BLUE)进入后端容器...$(NC)"
	@docker-compose exec backend bash

shell-mysql: ## 进入 MySQL 容器
	@echo "$(BLUE)进入 MySQL 容器...$(NC)"
	@docker-compose exec mysql mysql -u pinan -p pinandai

shell-redis: ## 进入 Redis 容器
	@echo "$(BLUE)进入 Redis 容器...$(NC)"
	@docker-compose exec redis redis-cli

clean: ## 清理 Docker 资源
	@echo "$(YELLOW)清理 Docker 资源...$(NC)"
	@./scripts/stop.sh --cleanup
	@docker system prune -f

clean-all: ## 清理所有 Docker 资源（包括数据卷）
	@echo "$(RED)警告: 这将删除所有数据！$(NC)"
	@read -p "确认删除所有数据？(y/N): " confirm && [ "$$confirm" = "y" ]
	@docker-compose down -v
	@docker system prune -a -f

backup: ## 备份数据库
	@echo "$(BLUE)备份数据库...$(NC)"
	@mkdir -p backups
	@docker-compose exec mysql mysqldump -u pinan -p pinandai > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)备份完成: backups/backup_$(shell date +%Y%m%d_%H%M%S).sql$(NC)"

restore: ## 恢复数据库 (使用: make restore FILE=backup.sql)
	@echo "$(BLUE)恢复数据库...$(NC)"
	@if [ -z "$(FILE)" ]; then echo "$(RED)请指定备份文件: make restore FILE=backup.sql$(NC)"; exit 1; fi
	@if [ ! -f "$(FILE)" ]; then echo "$(RED)备份文件不存在: $(FILE)$(NC)"; exit 1; fi
	@docker-compose exec -T mysql mysql -u pinan -p pinandai < $(FILE)
	@echo "$(GREEN)数据库恢复完成$(NC)"

update: ## 更新系统（重新构建并启动）
	@echo "$(BLUE)更新系统...$(NC)"
	@docker-compose down
	@docker-compose build --no-cache
	@docker-compose up -d
	@echo "$(GREEN)系统更新完成$(NC)"

dev-backend: ## 本地开发模式启动后端
	@echo "$(BLUE)本地开发模式启动后端...$(NC)"
	@cd admin_pinan && mvn spring-boot:run -Dspring-boot.run.profiles=dev

dev-frontend: ## 本地开发模式启动前端
	@echo "$(BLUE)本地开发模式启动前端...$(NC)"
	@cd web_pinan && npm run start:dev

test-backend: ## 运行后端测试
	@echo "$(BLUE)运行后端测试...$(NC)"
	@cd admin_pinan && mvn test

test-frontend: ## 运行前端测试
	@echo "$(BLUE)运行前端测试...$(NC)"
	@cd web_pinan && npm test

lint-frontend: ## 前端代码检查
	@echo "$(BLUE)前端代码检查...$(NC)"
	@cd web_pinan && npm run lint

format-frontend: ## 前端代码格式化
	@echo "$(BLUE)前端代码格式化...$(NC)"
	@cd web_pinan && npm run prettier

health: ## 检查服务健康状态
	@echo "$(BLUE)检查服务健康状态...$(NC)"
	@echo "前端服务: $(shell curl -s -o /dev/null -w "%{http_code}" http://localhost || echo "无法连接")"
	@echo "后端服务: $(shell curl -s -o /dev/null -w "%{http_code}" http://localhost:8088/actuator/health || echo "无法连接")"
	@echo "MySQL 服务: $(shell docker-compose exec -T mysql mysqladmin ping -h localhost --silent && echo "正常" || echo "异常")"
	@echo "Redis 服务: $(shell docker-compose exec -T redis redis-cli ping 2>/dev/null || echo "异常")"

monitor: ## 监控系统资源使用
	@echo "$(BLUE)监控系统资源使用...$(NC)"
	@docker stats --no-stream

info: ## 显示系统信息
	@echo "$(GREEN)admin_pinan 金融贷款管理系统$(NC)"
	@echo "版本: 3.8.8"
	@echo "技术栈: Spring Boot + React + MySQL + Redis"
	@echo ""
	@echo "$(YELLOW)访问地址:$(NC)"
	@echo "前端管理界面: http://localhost"
	@echo "后端 API:     http://localhost:8088"
	@echo "Druid 监控:   http://localhost:8088/druid"
	@echo ""
	@echo "$(YELLOW)默认账号:$(NC)"
	@echo "用户名: admin"
	@echo "密码:   admin123"

install: ## 安装项目依赖
	@echo "$(BLUE)安装项目依赖...$(NC)"
	@echo "检查 Docker 环境..."
	@docker --version
	@docker-compose --version
	@echo "$(GREEN)环境检查完成$(NC)"

# 开发相关命令
dev: check ## 开发环境一键启动
	@echo "$(BLUE)启动开发环境...$(NC)"
	@make start
	@echo "$(GREEN)开发环境启动完成$(NC)"
	@make info
