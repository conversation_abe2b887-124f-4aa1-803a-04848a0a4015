# admin_pinan 项目目录结构说明

## 完整目录结构

```
admin_pinan/
├── README.md                        # 项目说明文档
├── DOCKER_DEPLOYMENT.md             # Docker 部署指南
├── DIRECTORY_STRUCTURE.md           # 目录结构说明（本文件）
├── Makefile                         # 快速操作命令
├── docker-compose.yml              # Docker Compose 配置文件
├── .env.example                     # 环境变量示例文件
├── .gitignore                       # Git 忽略文件配置
│
├── admin_pinan/                     # 后端项目（Spring Boot）
│   ├── pom.xml                      # Maven 主配置文件
│   ├── admin/                       # Web 服务入口模块
│   │   ├── pom.xml                  # 模块配置文件
│   │   ├── src/main/java/           # Java 源代码
│   │   │   └── com/ali/
│   │   │       ├── AliApplication.java      # 主启动类
│   │   │       └── web/controller/          # 控制器层
│   │   └── src/main/resources/      # 资源文件
│   │       ├── application.yml      # 主配置文件
│   │       ├── application-dev.yml  # 开发环境配置
│   │       ├── application-docker.yml # Docker 环境配置
│   │       ├── logback.xml          # 日志配置
│   │       ├── mybatis/             # MyBatis 配置
│   │       └── i18n/                # 国际化资源
│   │
│   ├── framework/                   # 核心框架模块
│   │   ├── pom.xml
│   │   └── src/main/java/com/ali/framework/
│   │       ├── aspectj/             # AOP 切面
│   │       ├── config/              # 配置类
│   │       ├── datasource/          # 数据源配置
│   │       ├── interceptor/         # 拦截器
│   │       ├── manager/             # 管理器
│   │       ├── security/            # 安全配置
│   │       └── web/                 # Web 相关
│   │
│   ├── system/                      # 系统管理模块
│   │   ├── pom.xml
│   │   └── src/main/java/com/ali/
│   │       ├── system/              # 系统管理
│   │       │   ├── domain/          # 实体类
│   │       │   ├── mapper/          # 数据访问层
│   │       │   └── service/         # 业务逻辑层
│   │       └── biz/                 # 业务模块
│   │           ├── domain/          # 业务实体
│   │           ├── mapper/          # 业务数据访问
│   │           └── service/         # 业务服务
│   │
│   ├── biz/                         # 业务控制器模块
│   │   ├── pom.xml
│   │   └── src/main/java/com/ali/biz/
│   │       ├── controller/          # 业务控制器
│   │       ├── convert/             # 数据转换器
│   │       └── vo/                  # 视图对象
│   │
│   ├── common/                      # 通用工具模块
│   │   ├── pom.xml
│   │   └── src/main/java/com/ali/common/
│   │       ├── annotation/          # 自定义注解
│   │       ├── config/              # 通用配置
│   │       ├── constant/            # 常量定义
│   │       ├── core/                # 核心类
│   │       ├── enums/               # 枚举类
│   │       ├── exception/           # 异常处理
│   │       ├── filter/              # 过滤器
│   │       └── utils/               # 工具类
│   │
│   ├── generator/                   # 代码生成模块
│   │   ├── pom.xml
│   │   └── src/main/
│   │       ├── java/com/ali/generator/  # 代码生成逻辑
│   │       └── resources/vm/            # 代码模板
│   │
│   ├── quartz/                      # 定时任务模块
│   │   ├── pom.xml
│   │   └── src/main/java/com/ali/quartz/
│   │
│   └── sql/                         # 数据库脚本
│       └── base_sql.sql             # 基础数据库结构
│
├── web_pinan/                       # 前端项目（React）
│   ├── package.json                 # npm 配置文件
│   ├── tsconfig.json                # TypeScript 配置
│   ├── jest.config.ts               # 测试配置
│   ├── config/                      # 构建配置
│   │   ├── config.ts                # 主配置
│   │   ├── defaultSettings.ts       # 默认设置
│   │   ├── proxy.ts                 # 代理配置
│   │   └── routes.ts                # 路由配置
│   ├── public/                      # 静态资源
│   ├── src/                         # 源代码
│   │   ├── app.tsx                  # 应用入口
│   │   ├── global.less              # 全局样式
│   │   ├── components/              # 通用组件
│   │   ├── pages/                   # 页面组件
│   │   ├── services/                # API 服务
│   │   ├── types/                   # 类型定义
│   │   └── utils/                   # 工具函数
│   ├── mock/                        # Mock 数据
│   └── dist/                        # 构建输出
│
├── pinan/                           # 移动端项目（uni-app）
│   ├── package.json                 # 项目配置
│   ├── manifest.json                # 应用配置
│   ├── pages.json                   # 页面配置
│   ├── App.vue                      # 应用入口
│   ├── main.js                      # 主入口文件
│   ├── pages/                       # 页面文件
│   ├── static/                      # 静态资源
│   └── utils/                       # 工具函数
│
├── down_app/                        # 下载页面项目（React）
│   ├── package.json                 # 项目配置
│   ├── tsconfig.json                # TypeScript 配置
│   ├── public/                      # 静态资源
│   ├── src/                         # 源代码
│   └── build/                       # 构建输出
│
├── docker/                          # Docker 配置文件
│   ├── backend/                     # 后端 Docker 配置
│   │   └── Dockerfile               # 后端镜像构建文件
│   ├── frontend/                    # 前端 Docker 配置
│   │   └── Dockerfile               # 前端镜像构建文件
│   ├── nginx/                       # Nginx 配置
│   │   ├── nginx.conf               # 主配置文件
│   │   └── conf.d/
│   │       └── default.conf         # 站点配置
│   ├── mysql/                       # MySQL 配置
│   │   ├── conf/
│   │   │   └── my.cnf               # MySQL 配置文件
│   │   └── data/                    # 数据目录（运行时创建）
│   └── redis/                       # Redis 配置
│       ├── redis.conf               # Redis 配置文件
│       └── data/                    # 数据目录（运行时创建）
│
├── scripts/                         # 部署和管理脚本
│   ├── start.sh                     # 系统启动脚本
│   ├── stop.sh                      # 系统停止脚本
│   ├── logs.sh                      # 日志查看脚本
│   └── check-env.sh                 # 环境检查脚本
│
└── logs/                            # 日志目录（运行时创建）
    ├── backend/                     # 后端日志
    ├── nginx/                       # Nginx 日志
    └── mysql/                       # MySQL 日志
```

## 核心模块说明

### 后端模块（admin_pinan/）

#### 1. admin 模块
- **作用**: Web 服务入口，包含主启动类和控制器
- **主要文件**:
  - `AliApplication.java`: Spring Boot 主启动类
  - `web/controller/`: REST API 控制器
  - `application*.yml`: 配置文件

#### 2. framework 模块
- **作用**: 核心框架功能，包含安全、配置、切面等
- **主要组件**:
  - `security/`: Spring Security 配置
  - `config/`: 系统配置类
  - `aspectj/`: AOP 切面
  - `datasource/`: 数据源配置

#### 3. system 模块
- **作用**: 系统管理功能和业务实体
- **主要功能**:
  - 用户管理、角色管理、菜单管理
  - 贷款用户、贷款信息、收款信息等业务实体

#### 4. biz 模块
- **作用**: 业务控制器和视图对象
- **主要内容**:
  - 业务 API 控制器
  - 数据转换器
  - 视图对象定义

#### 5. common 模块
- **作用**: 通用工具和基础组件
- **主要内容**:
  - 工具类、常量、枚举
  - 异常处理、注解定义
  - 核心基础类

#### 6. generator 模块
- **作用**: 代码生成工具
- **功能**: 根据数据库表自动生成 CRUD 代码

#### 7. quartz 模块
- **作用**: 定时任务管理
- **功能**: 定时任务的创建、调度和管理

### 前端模块（web_pinan/）

#### 主要目录结构
- `src/pages/`: 页面组件，按功能模块组织
- `src/components/`: 通用组件库
- `src/services/`: API 服务封装
- `src/types/`: TypeScript 类型定义
- `config/`: 构建和路由配置

### Docker 配置（docker/）

#### 各服务配置
- `backend/`: 后端服务 Docker 配置
- `frontend/`: 前端服务 Docker 配置
- `nginx/`: Web 服务器配置
- `mysql/`: 数据库配置
- `redis/`: 缓存服务配置

### 脚本工具（scripts/）

#### 管理脚本
- `start.sh`: 一键启动系统
- `stop.sh`: 停止系统服务
- `logs.sh`: 查看服务日志
- `check-env.sh`: 环境检查工具

## 配置文件说明

### 主要配置文件

1. **docker-compose.yml**: Docker 服务编排配置
2. **application*.yml**: Spring Boot 应用配置
3. **package.json**: Node.js 项目配置
4. **nginx.conf**: Web 服务器配置
5. **my.cnf**: MySQL 数据库配置
6. **redis.conf**: Redis 缓存配置

### 环境配置

- `.env.example`: 环境变量模板
- `application-dev.yml`: 开发环境配置
- `application-docker.yml`: Docker 环境配置
- `proxy.ts`: 前端代理配置

## 数据存储

### 持久化数据
- `docker/mysql/data/`: MySQL 数据文件
- `docker/redis/data/`: Redis 数据文件
- `logs/`: 应用日志文件
- `upload/`: 文件上传目录

### 临时数据
- `target/`: Maven 构建输出
- `node_modules/`: Node.js 依赖
- `dist/`: 前端构建输出

## 开发指南

### 添加新功能模块

1. **后端开发**:
   - 在 `system` 模块添加实体类
   - 在 `biz` 模块添加控制器
   - 使用 `generator` 模块生成基础代码

2. **前端开发**:
   - 在 `src/pages/` 添加页面组件
   - 在 `src/services/` 添加 API 服务
   - 在 `config/routes.ts` 配置路由

3. **数据库变更**:
   - 更新 `sql/base_sql.sql` 脚本
   - 重新构建 Docker 镜像

### 配置修改

1. **环境配置**: 修改 `.env` 文件
2. **应用配置**: 修改对应的 `application*.yml` 文件
3. **Docker 配置**: 修改 `docker-compose.yml` 或相关 Dockerfile
4. **Nginx 配置**: 修改 `docker/nginx/` 下的配置文件

这个目录结构设计遵循了模块化、分层架构的原则，便于开发、维护和扩展。
