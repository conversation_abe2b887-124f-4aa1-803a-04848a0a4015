# 前端构建专用 Dockerfile
# 仅用于构建静态文件，不包含 Nginx

FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置 npm 镜像源（加速构建）
RUN npm config set registry https://registry.npmmirror.com

# 安装必要的工具
RUN apk add --no-cache git

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production --silent

# 复制源代码
COPY . .

# 设置构建参数
ARG API_BASE_URL=http://localhost:8088
ENV REACT_APP_API_BASE_URL=$API_BASE_URL

# 构建应用
RUN npm run build

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建输出目录并复制构建产物
RUN mkdir -p /app/dist && \
    cp -r build/* /app/dist/ 2>/dev/null || cp -r dist/* /app/dist/ 2>/dev/null || true

# 设置权限
RUN chmod -R 755 /app/dist

# 保持容器运行（用于数据卷共享）
CMD ["tail", "-f", "/dev/null"]
