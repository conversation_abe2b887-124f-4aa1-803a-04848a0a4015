# SSL 证书配置说明

## 概述

此目录用于存放 SSL 证书文件，支持 HTTPS 访问。

## 文件结构

```
ssl/
├── README.md           # 本说明文件
├── cert.pem           # SSL 证书文件
├── key.pem            # SSL 私钥文件
└── dhparam.pem        # DH 参数文件（可选）
```

## 生成自签名证书（开发环境）

```bash
# 进入 SSL 目录
cd docker/nginx/ssl

# 生成私钥
openssl genrsa -out key.pem 2048

# 生成证书签名请求
openssl req -new -key key.pem -out cert.csr

# 生成自签名证书
openssl x509 -req -days 365 -in cert.csr -signkey key.pem -out cert.pem

# 生成 DH 参数（可选，增强安全性）
openssl dhparam -out dhparam.pem 2048

# 清理临时文件
rm cert.csr
```

## 使用 Let's Encrypt 证书（生产环境）

```bash
# 安装 certbot
sudo apt-get install certbot

# 获取证书
sudo certbot certonly --standalone -d your-domain.com

# 复制证书到项目目录
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem docker/nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem docker/nginx/ssl/key.pem
```

## 启用 HTTPS

1. 确保证书文件存在
2. 修改 `docker/nginx/conf.d/standalone.conf`，取消 HTTPS 配置的注释
3. 重新启动服务

```bash
# 重新启动 Nginx 服务
docker-compose restart nginx
```

## 安全建议

1. **私钥保护**: 确保私钥文件权限为 600
2. **证书更新**: 定期更新证书，建议使用自动化工具
3. **强加密**: 使用现代加密套件和协议
4. **HSTS**: 启用 HTTP 严格传输安全

## 故障排除

### 证书验证失败

```bash
# 检查证书有效性
openssl x509 -in cert.pem -text -noout

# 检查私钥匹配
openssl rsa -in key.pem -check
```

### Nginx 配置测试

```bash
# 测试 Nginx 配置
docker-compose exec nginx nginx -t

# 重新加载配置
docker-compose exec nginx nginx -s reload
```
