# admin_pinan 金融贷款管理系统

## 项目简介

admin_pinan 是一个基于 Spring Boot + React 的现代化金融贷款管理系统，专为中小型金融机构设计，提供完整的贷款业务流程管理功能。

### 主要功能

- 🏦 **贷款用户管理** - 借款人信息管理、身份验证、银行卡管理
- 💰 **贷款业务管理** - 贷款申请、审批、放款、还款全流程
- 💳 **收款信息管理** - 还款账户管理、收款记录跟踪
- 👥 **系统用户管理** - 多角色权限管理、操作日志记录
- 🔧 **系统管理** - 菜单管理、角色权限、参数配置
- 📊 **数据监控** - Druid 数据库监控、系统性能监控
- 🛠️ **代码生成** - 自动生成 CRUD 代码，提高开发效率

### 技术栈

**后端技术**
- Java 17 + Spring Boot 2.5.15
- Spring Security + JWT 认证
- MyBatis + MySQL 8.0
- Redis 缓存 + Druid 连接池
- Swagger/Knife4j API 文档

**前端技术**
- React 18 + TypeScript
- Ant Design Pro 组件库
- UmiJS 4.x 构建工具
- Axios HTTP 客户端

**部署技术**
- Docker + Docker Compose
- Nginx 反向代理
- 多阶段构建优化

## 快速开始

### 环境要求

- Windows 10/11 (版本 2004+)
- Docker Desktop for Windows
- WSL2 (Windows Subsystem for Linux 2)
- 8GB+ 内存，20GB+ 磁盘空间

### 一键部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd admin_pinan

# 2. 环境检查
./scripts/check-env.sh

# 3. 一键启动
./scripts/start.sh
```

### 访问系统

部署完成后，您可以通过以下地址访问系统：

- **管理后台**: http://localhost
- **API 接口**: http://localhost:8088
- **数据库监控**: http://localhost:8088/druid

**默认账号**
- 用户名: `admin`
- 密码: `admin123`

## 详细部署指南

详细的部署说明请参考 [Docker 部署指南](DOCKER_DEPLOYMENT.md)

## 项目结构

```
admin_pinan/
├── admin_pinan/                 # 后端项目 (Spring Boot)
│   ├── admin/                   # Web 入口模块
│   ├── framework/               # 核心框架模块
│   ├── system/                  # 系统管理模块
│   ├── biz/                     # 业务逻辑模块
│   ├── common/                  # 通用工具模块
│   ├── generator/               # 代码生成模块
│   ├── quartz/                  # 定时任务模块
│   └── sql/                     # 数据库脚本
├── web_pinan/                   # 前端项目 (React)
├── pinan/                       # 移动端项目 (uni-app)
├── down_app/                    # 下载页面 (React)
├── docker/                      # Docker 配置文件
├── scripts/                     # 部署脚本
└── logs/                        # 日志目录
```

## 常用命令

### 服务管理

```bash
# 启动所有服务
./scripts/start.sh

# 停止所有服务
./scripts/stop.sh

# 停止并清理资源
./scripts/stop.sh --cleanup

# 查看服务状态
docker-compose ps
```

### 日志查看

```bash
# 查看所有服务日志
./scripts/logs.sh all -f

# 查看后端服务日志
./scripts/logs.sh backend -f

# 查看最近 100 行日志
./scripts/logs.sh mysql -n 100
```

### 服务操作

```bash
# 重启特定服务
docker-compose restart backend

# 进入容器
docker-compose exec backend bash
docker-compose exec mysql mysql -u pinan -p

# 查看容器资源使用
docker stats
```

## 开发指南

### 后端开发

```bash
# 进入后端目录
cd admin_pinan

# Maven 构建
mvn clean package -DskipTests

# 本地运行
java -jar admin/target/admin.jar
```

### 前端开发

```bash
# 进入前端目录
cd web_pinan

# 安装依赖
npm install

# 开发模式启动
npm run start:dev

# 构建生产版本
npm run build
```

## 配置说明

### 环境配置

复制 `.env.example` 为 `.env` 并根据需要修改配置：

```bash
cp .env.example .env
```

### 数据库配置

默认数据库配置：
- 数据库: `pinandai`
- 用户名: `pinan`
- 密码: `pinan123`

### Redis 配置

默认 Redis 配置：
- 端口: `6379`
- 数据库: `2`
- 无密码

## 监控与维护

### 健康检查

- 后端健康检查: http://localhost:8088/actuator/health
- 前端健康检查: http://localhost/health

### 性能监控

- Druid 监控面板: http://localhost:8088/druid
- 用户名: `admin`，密码: `admin123`

### 日志管理

日志文件位置：
- 后端日志: `logs/backend/`
- Nginx 日志: `logs/nginx/`
- 数据库日志: Docker 容器内

## 故障排除

### 常见问题

1. **端口冲突**: 修改 `docker-compose.yml` 中的端口映射
2. **内存不足**: 增加 Docker Desktop 内存限制
3. **构建失败**: 清理 Docker 缓存并重新构建
4. **数据库连接失败**: 检查 MySQL 服务状态和配置

### 获取帮助

```bash
# 查看脚本帮助
./scripts/start.sh --help
./scripts/stop.sh --help
./scripts/logs.sh --help
```

## 安全说明

### 生产环境配置

在生产环境中，请务必：

1. 修改所有默认密码
2. 启用 HTTPS
3. 配置防火墙规则
4. 定期备份数据
5. 监控系统日志

### 数据备份

```bash
# 备份数据库
docker-compose exec mysql mysqldump -u pinan -p pinandai > backup.sql

# 恢复数据库
docker-compose exec -T mysql mysql -u pinan -p pinandai < backup.sql
```

## 许可证

本项目采用 [MIT 许可证](LICENSE)

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目。

## 联系我们

如有问题或建议，请通过以下方式联系：

- 提交 Issue: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

---

**注意**: 本系统仅供学习和研究使用，在生产环境中使用前请进行充分的安全评估和测试。
