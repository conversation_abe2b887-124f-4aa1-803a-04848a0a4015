services:
  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    container_name: admin_pinan_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: pinandai
      MYSQL_USER: pinan
      <PERSON>QL_PASSWORD: pinan123
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./admin_pinan/sql:/docker-entrypoint-initdb.d
      - ./docker/mysql/conf:/etc/mysql/conf.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - pinan_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    container_name: admin_pinan_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - pinan_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Java 后端应用
  backend:
    build:
      context: ./admin_pinan
      dockerfile: ../docker/backend/Dockerfile
    container_name: admin_pinan_backend
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_DATABASE: pinandai
      MYSQL_USERNAME: pinan
      MYSQL_PASSWORD: pinan123
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_DATABASE: 2
      TZ: Asia/Shanghai
    ports:
      - "8088:8088"
    volumes:
      - backend_logs:/app/logs
      - backend_upload:/app/upload
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - pinan_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8088/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端管理界面
  frontend:
    build:
      context: ./web_pinan
      dockerfile: ../docker/frontend/Dockerfile
      args:
        API_BASE_URL: http://localhost:8088
    container_name: admin_pinan_frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # 挂载 Nginx 配置文件
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/conf.d/default.conf:/etc/nginx/conf.d/default.conf:ro
      # 日志目录
      - nginx_logs:/var/log/nginx
    depends_on:
      - backend
    networks:
      - pinan_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  backend_upload:
    driver: local
  nginx_logs:
    driver: local

networks:
  pinan_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
