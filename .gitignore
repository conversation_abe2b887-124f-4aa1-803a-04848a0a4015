# admin_pinan 项目 .gitignore 文件

# ==========================================
# 环境配置文件
# ==========================================
.env
.env.local
.env.production

# ==========================================
# Docker 相关
# ==========================================
# Docker 数据卷
docker/mysql/data/
docker/redis/data/

# ==========================================
# 日志文件
# ==========================================
logs/
*.log
*.log.*

# ==========================================
# 上传文件
# ==========================================
upload/
uploads/

# ==========================================
# Java 相关
# ==========================================
# Maven
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

# Gradle
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# IDE
.idea/
*.iws
*.iml
*.ipr
.vscode/
*.swp
*.swo

# Java
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# ==========================================
# Node.js 相关
# ==========================================
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
build/
.next/
out/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# ESLint cache
.eslintcache

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ==========================================
# 操作系统相关
# ==========================================
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ==========================================
# 临时文件
# ==========================================
*.tmp
*.temp
*.bak
*.backup
*.orig
*.rej
*.patch

# ==========================================
# 数据库文件
# ==========================================
*.db
*.sqlite
*.sqlite3

# ==========================================
# 证书和密钥文件
# ==========================================
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# ==========================================
# 配置文件（包含敏感信息）
# ==========================================
config/local.yml
config/production.yml
application-local.yml
application-production.yml

# ==========================================
# 测试相关
# ==========================================
# Test results
test-results/
coverage/
.nyc_output/

# ==========================================
# 编辑器和 IDE
# ==========================================
# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Sublime Text
*.sublime-workspace
*.sublime-project

# Atom
.atom/

# ==========================================
# 其他
# ==========================================
# Backup files
*.bak
*.backup
*.old

# Archive files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Logs and databases
*.log
*.sql
*.sqlite

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# Lock files (keep package-lock.json for reproducible builds)
# yarn.lock

# Local environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local
