<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ali.biz.mapper.LoanUserMapper">


    <sql id="selectLoanUserVo">
        select loanUser.id,
               loanUser.user_name,
               loanUser.password,
               loanUser.phone_number,
               loanUser.id_card,
               loanUser.open_bank,
               loanUser.bank_number,
               loanUser.open_address,
               loanUser.sort,
               loanUser.status,
               loanUser.user_type,
               loanUser.login_ip,
               loanUser.login_date,
               loanUser.create_by,
               loanUser.create_time,
               loanUser.update_by,
               loanUser.update_time,
               loanUser.remark,
               loanInfo.id             as loanInfoId,
               loanInfo.loan_amount    as loanAmount,
               loanInfo.start_time     as startTime,
               loanInfo.end_time       as endTime,
               loanInfo.expect_amount  as expectAmount,
               loanInfo.payable_amount as payableAmount,
               loanInfo.status         as loanInfoStatus,
               payInfo.id              as payInfoId,
               payInfo.open_name       as payOpenName,
               payInfo.open_bank       as payOpenBank,
               payInfo.bank_number     as payBankNumber,
               payInfo.open_address    as payOpenAddress,
               payInfo.image_info      as imageInfo,
               payInfo.remark      as payRemark
        from biz_loan_user loanUser
                 left join biz_loan_info loanInfo on loanUser.id = loanInfo.loan_user_id
                 left join biz_receive_pay_info payInfo on loanUser.id = payInfo.loan_user_id
    </sql>

    <select id="selectLoanUserList" parameterType="com.ali.biz.domain.LoanUser"
            resultType="com.ali.biz.domain.LoanUser">
        <include refid="selectLoanUserVo"/>
        <where>
            <if test="userName != null  and userName != ''">
                and loanUser.user_name like concat('%', #{userName}, '%')
            </if>
            <if test="createBy != null and createBy != ''">
                AND loanUser.create_by like concat('%', #{createBy}, '%')
            </if>
            <if test="password != null  and password != ''">
                and loanUser.password = #{password}
            </if>
            <if test="phoneNumber != null  and phoneNumber != ''">
                and loanUser.phone_number = #{phoneNumber}
            </if>
            <if test="idCard != null  and idCard != ''">
                and loanUser.id_card = #{idCard}
            </if>
            <if test="openBank != null  and openBank != ''">
                and loanUser.open_bank = #{openBank}
            </if>
            <if test="bankNumber != null  and bankNumber != ''">
                and loanUser.bank_number = #{bankNumber}
            </if>
            <if test="openAddress != null  and openAddress != ''">
                and loanUser.open_address = #{openAddress}
            </if>
            <if test="sort != null ">
                and loanUser.sort = #{sort}
            </if>
            <if test="status != null  and status != ''">
                and loanUser.status = #{status}
            </if>
            <if test="userType != null  and userType != ''">
                and loanUser.user_type = #{userType}
            </if>
            <if test="loginIp != null  and loginIp != ''">
                and loanUser.login_ip = #{loginIp}
            </if>
            <if test="loginDate != null ">
                and loanUser.login_date = #{loginDate}
            </if>
        </where>
    </select>

    <select id="selectLoanUserById" parameterType="Long"
            resultType="com.ali.biz.domain.LoanUser">
        <include refid="selectLoanUserVo"/>
        where loanUser.id = #{id}
    </select>
    <select id="selectLoanUserByPhone" resultType="com.ali.biz.domain.LoanUser">
        <include refid="selectLoanUserVo"/>
        where loanUser.phone_number = #{phoneNumber}
    </select>

    <insert id="insertLoanUser" parameterType="com.ali.biz.domain.LoanUser" useGeneratedKeys="true" keyProperty="id">
        insert into biz_loan_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="password != null">password,</if>
            <if test="phoneNumber != null">phone_number,</if>
            <if test="idCard != null">id_card,</if>
            <if test="openBank != null and openBank != ''">open_bank,</if>
            <if test="bankNumber != null and bankNumber != ''">bank_number,</if>
            <if test="openAddress != null">open_address,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="userType != null">user_type,</if>
            <if test="loginIp != null">login_ip,</if>
            <if test="loginDate != null">login_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="password != null">#{password},</if>
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="openBank != null and openBank != ''">#{openBank},</if>
            <if test="bankNumber != null and bankNumber != ''">#{bankNumber},</if>
            <if test="openAddress != null">#{openAddress},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="userType != null">#{userType},</if>
            <if test="loginIp != null">#{loginIp},</if>
            <if test="loginDate != null">#{loginDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateLoanUser" parameterType="com.ali.biz.domain.LoanUser">
        update biz_loan_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="password != null">password = #{password},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="openBank != null and openBank != ''">open_bank = #{openBank},</if>
            <if test="bankNumber != null and bankNumber != ''">bank_number = #{bankNumber},</if>
            <if test="openAddress != null">open_address = #{openAddress},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="loginIp != null">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLoanUserById" parameterType="Long">
        delete
        from biz_loan_user
        where id = #{id}
    </delete>

    <delete id="deleteLoanUserByIds" parameterType="String">
        delete from biz_loan_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
