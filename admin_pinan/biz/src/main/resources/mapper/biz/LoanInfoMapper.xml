<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ali.biz.mapper.LoanInfoMapper">

    <sql id="selectLoanInfoVo">
        select id,
               loan_user_id,
               loan_amount,
               start_time,
               end_time,
               expect_amount,
               payable_amount,
               status,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from biz_loan_info loan
    </sql>

    <select id="selectLoanInfoList" parameterType="com.ali.biz.domain.LoanInfo"
            resultType="com.ali.biz.domain.LoanInfo">
        <include refid="selectLoanInfoVo"/>
        <where>
            <if test="loanUserId != null ">
                and loan_user_id = #{loanUserId}
            </if>
            <if test="loanAmount != null ">
                and loan_amount = #{loanAmount}
            </if>
            <if test="startTime != null ">
                and start_time = #{startTime}
            </if>
            <if test="endTime != null ">
                and end_time = #{endTime}
            </if>
            <if test="expectAmount != null ">
                and expect_amount = #{expectAmount}
            </if>
            <if test="payableAmount != null ">
                and payable_amount = #{payableAmount}
            </if>
            <if test="status != null  and status != ''">
                and status = #{status}
            </if>
        </where>
    </select>

    <select id="selectLoanInfoById" parameterType="Long"
            resultType="com.ali.biz.domain.LoanInfo">
        <include refid="selectLoanInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertLoanInfo" parameterType="com.ali.biz.domain.LoanInfo" useGeneratedKeys="true" keyProperty="id">
        insert into biz_loan_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="loanUserId != null">loan_user_id,</if>
            <if test="loanAmount != null">loan_amount,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="expectAmount != null">expect_amount,</if>
            <if test="payableAmount != null">payable_amount,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="loanUserId != null">#{loanUserId},</if>
            <if test="loanAmount != null">#{loanAmount},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="expectAmount != null">#{expectAmount},</if>
            <if test="payableAmount != null">#{payableAmount},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateLoanInfo" parameterType="com.ali.biz.domain.LoanInfo">
        update biz_loan_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="loanUserId != null">loan_user_id = #{loanUserId},</if>
            <if test="loanAmount != null">loan_amount = #{loanAmount},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="expectAmount != null">expect_amount = #{expectAmount},</if>
            <if test="payableAmount != null">payable_amount = #{payableAmount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLoanInfoById" parameterType="Long">
        delete
        from biz_loan_info
        where id = #{id}
    </delete>

    <delete id="deleteLoanInfoByIds" parameterType="String">
        delete from biz_loan_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
