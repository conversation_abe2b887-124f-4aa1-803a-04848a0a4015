<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ali.biz.mapper.ReceivePayInfoMapper">


    <sql id="selectReceivePayInfoVo">
        select id,
               loan_user_id,
               open_name,
               open_bank,
               bank_number,
               open_address,
               image_info,
               status,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from biz_receive_pay_info
    </sql>

    <select id="selectReceivePayInfoList" parameterType="com.ali.biz.domain.ReceivePayInfo"
            resultType="com.ali.biz.domain.ReceivePayInfo">
        <include refid="selectReceivePayInfoVo"/>
        <where>
            <if test="loanUserId != null ">
                and loan_user_id = #{loanUserId}
            </if>
            <if test="openBank != null  and openBank != ''">
                and open_bank = #{openBank}
            </if>
            <if test="bankNumber != null  and bankNumber != ''">
                and bank_number = #{bankNumber}
            </if>
            <if test="openAddress != null  and openAddress != ''">
                and open_address = #{openAddress}
            </if>
            <if test="imageInfo != null  and imageInfo != ''">
                and image_info = #{imageInfo}
            </if>
            <if test="status != null  and status != ''">
                and status = #{status}
            </if>
        </where>
    </select>

    <select id="selectReceivePayInfoById" parameterType="Long"
            resultType="com.ali.biz.domain.ReceivePayInfo">
        <include refid="selectReceivePayInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertReceivePayInfo" parameterType="com.ali.biz.domain.ReceivePayInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into biz_receive_pay_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="loanUserId != null">loan_user_id,</if>
            <if test="openName != null">open_name,</if>
            <if test="openBank != null and openBank != ''">open_bank,</if>
            <if test="bankNumber != null and bankNumber != ''">bank_number,</if>
            <if test="openAddress != null">open_address,</if>
            <if test="imageInfo != null">image_info,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="loanUserId != null">#{loanUserId},</if>
            <if test="openName != null">#{openName},</if>
            <if test="openBank != null and openBank != ''">#{openBank},</if>
            <if test="bankNumber != null and bankNumber != ''">#{bankNumber},</if>
            <if test="openAddress != null">#{openAddress},</if>
            <if test="imageInfo != null">#{imageInfo},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateReceivePayInfo" parameterType="com.ali.biz.domain.ReceivePayInfo">
        update biz_receive_pay_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="loanUserId != null">loan_user_id = #{loanUserId},</if>
            <if test="openName != null">open_name = #{openName},</if>
            <if test="openBank != null and openBank != ''">open_bank = #{openBank},</if>
            <if test="bankNumber != null and bankNumber != ''">bank_number = #{bankNumber},</if>
            <if test="openAddress != null">open_address = #{openAddress},</if>
            <if test="imageInfo != null">image_info = #{imageInfo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteReceivePayInfoById" parameterType="Long">
        delete
        from biz_receive_pay_info
        where id = #{id}
    </delete>

    <delete id="deleteReceivePayInfoByIds" parameterType="String">
        delete from biz_receive_pay_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
