package com.ali.biz.convert;

import com.ali.biz.domain.ReceivePayInfo;
import com.ali.biz.vo.postvo.ReceivePayInfoPostVo;
import com.ali.biz.vo.putvo.ReceivePayInfoPutVo;
import org.springframework.util.ObjectUtils;


/**
 * 收款信息 转换类
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
public class ReceivePayInfoConvert {
    /**
     * ReceivePayInfoPostVo 转换为 ReceivePayInfo
     */
    public static ReceivePayInfo postVoToReceivePayInfo(ReceivePayInfoPostVo postVo) {
        ReceivePayInfo receivePayInfo = new ReceivePayInfo();
        if (ObjectUtils.isEmpty(postVo)) {
            return receivePayInfo;
        }
        receivePayInfo.setLoanUserId(postVo.getLoanUserId());
        receivePayInfo.setOpenName(postVo.getOpenName());
        receivePayInfo.setOpenBank(postVo.getOpenBank());
        receivePayInfo.setBankNumber(postVo.getBankNumber());
        receivePayInfo.setOpenAddress(postVo.getOpenAddress());
        receivePayInfo.setImageInfo(postVo.getImageInfo());
        receivePayInfo.setStatus(postVo.getStatus());
        receivePayInfo.setRemark(postVo.getRemark());
        return receivePayInfo;
    }

    /**
     * ReceivePayInfoPutVo 转换为 ReceivePayInfo
     */
    public static ReceivePayInfo putVoToReceivePayInfo(Long id, ReceivePayInfoPutVo putVo) {
        ReceivePayInfo receivePayInfo = new ReceivePayInfo();
        if (ObjectUtils.isEmpty(putVo)) {
            return receivePayInfo;
        }
        receivePayInfo.setId(id);
        receivePayInfo.setLoanUserId(putVo.getLoanUserId());
        receivePayInfo.setOpenName(putVo.getOpenName());
        receivePayInfo.setOpenBank(putVo.getOpenBank());
        receivePayInfo.setBankNumber(putVo.getBankNumber());
        receivePayInfo.setOpenAddress(putVo.getOpenAddress());
        receivePayInfo.setImageInfo(putVo.getImageInfo());
        receivePayInfo.setStatus(putVo.getStatus());
        receivePayInfo.setRemark(putVo.getRemark());
        return receivePayInfo;
    }

}
