package com.ali.biz.controller;

import com.ali.biz.convert.LoanUserConvert;
import com.ali.biz.domain.LoanUser;
import com.ali.biz.service.ILoanUserService;
import com.ali.biz.vo.postvo.LoanUserPostVo;
import com.ali.biz.vo.putvo.LoanUserPutVo;
import com.ali.common.annotation.Log;
import com.ali.common.core.controller.BaseController;
import com.ali.common.core.domain.AjaxResult;
import com.ali.common.enums.BusinessType;
import com.ali.common.utils.SecurityUtils;
import com.ali.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 贷款用户Controller
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Api(tags = "贷款用户相关接口")
@Validated
@RestController
public class LoanUserController extends BaseController {
    @Autowired
    private ILoanUserService loanUserService;

    @ApiOperation("查询贷款用户列表")
    @PreAuthorize("@ss.hasPermi('biz:loanUser:list')")
    @GetMapping("/biz/loanUser/list")
    public AjaxResult<LoanUser> list(LoanUser loanUser) {
        if (!"ad_min".equals(getUsername()))
            loanUser.setCreateBy(getUsername());
        startPage();
        return AjaxResult.getDataTable(loanUserService.selectLoanUserList(loanUser));
    }

    @ApiOperation("导出贷款用户列表")
    @PreAuthorize("@ss.hasPermi('biz:loanUser:export')")
    @Log(title = "贷款用户", businessType = BusinessType.EXPORT)
    @PostMapping("/biz/loanUser/export")
    public void export(HttpServletResponse response, LoanUser loanUser) {
        List<LoanUser> list = loanUserService.selectLoanUserList(loanUser);
        ExcelUtil<LoanUser> util = new ExcelUtil<>(LoanUser.class);
        util.exportExcel(response, list, "贷款用户数据");
    }

    @ApiOperation("获取贷款用户详细信息")
    @PreAuthorize("@ss.hasPermi('biz:loanUser:query')")
    @GetMapping(value = "/biz/loanUser/{id}")
    public AjaxResult<LoanUser> getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(loanUserService.selectLoanUserById(id));
    }

    @ApiOperation("新增贷款用户")
    @PreAuthorize("@ss.hasPermi('biz:loanUser:add')")
    @Log(title = "贷款用户", businessType = BusinessType.INSERT)
    @PostMapping("/biz/loanUser")
    public AjaxResult<String> add(@Valid @RequestBody LoanUserPostVo postVo) {
        return toAjax(loanUserService.insertLoanUser(LoanUserConvert.postVoToLoanUser(postVo)));
    }

    @ApiOperation("修改贷款用户")
    @PreAuthorize("@ss.hasPermi('biz:loanUser:edit')")
    @Log(title = "贷款用户", businessType = BusinessType.UPDATE)
    @PutMapping("/biz/loanUser/{id}")
    public AjaxResult<String> edit(@PathVariable("id") Long id,
                                   @Valid @RequestBody LoanUserPutVo putVo) {
        LoanUser loanUser = LoanUserConvert.putVoToLoanUser(id, putVo);
        loanUser.setPassword(SecurityUtils.encryptPassword(loanUser.getPhoneNumber()));
        return toAjax(loanUserService.updateLoanUser(loanUser));
    }

    @ApiOperation("删除贷款用户")
    @PreAuthorize("@ss.hasPermi('biz:loanUser:remove')")
    @Log(title = "贷款用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/biz/loanUser/{ids}")
    public AjaxResult<String> remove(@PathVariable Long[] ids) {
        return toAjax(loanUserService.deleteLoanUserByIds(ids));
    }
}
