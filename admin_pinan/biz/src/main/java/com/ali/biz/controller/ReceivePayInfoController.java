package com.ali.biz.controller;

import com.ali.biz.convert.ReceivePayInfoConvert;
import com.ali.biz.domain.ReceivePayInfo;
import com.ali.biz.service.IReceivePayInfoService;
import com.ali.biz.vo.postvo.ReceivePayInfoPostVo;
import com.ali.biz.vo.putvo.ReceivePayInfoPutVo;
import com.ali.common.annotation.Log;
import com.ali.common.core.controller.BaseController;
import com.ali.common.core.domain.AjaxResult;
import com.ali.common.enums.BusinessType;
import com.ali.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 收款信息Controller
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Api(tags = "收款信息相关接口")
@Validated
@RestController
public class ReceivePayInfoController extends BaseController {
    @Autowired
    private IReceivePayInfoService receivePayInfoService;

    @ApiOperation("查询收款信息列表")
    @PreAuthorize("@ss.hasPermi('biz:payInfo:list')")
    @GetMapping("/biz/payInfo/list")
    public AjaxResult<ReceivePayInfo> list(ReceivePayInfo receivePayInfo) {
        startPage();
        return AjaxResult.getDataTable(receivePayInfoService.selectReceivePayInfoList(receivePayInfo));
    }

    @ApiOperation("导出收款信息列表")
    @PreAuthorize("@ss.hasPermi('biz:payInfo:export')")
    @Log(title = "收款信息", businessType = BusinessType.EXPORT)
    @PostMapping("/biz/payInfo/export")
    public void export(HttpServletResponse response, ReceivePayInfo receivePayInfo) {
        List<ReceivePayInfo> list = receivePayInfoService.selectReceivePayInfoList(receivePayInfo);
        ExcelUtil<ReceivePayInfo> util = new ExcelUtil<>(ReceivePayInfo.class);
        util.exportExcel(response, list, "收款信息数据");
    }

    @ApiOperation("获取收款信息详细信息")
    @PreAuthorize("@ss.hasPermi('biz:payInfo:query')")
    @GetMapping(value = "/biz/payInfo/{id}")
    public AjaxResult<ReceivePayInfo> getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(receivePayInfoService.selectReceivePayInfoById(id));
    }

    @ApiOperation("新增收款信息")
    @PreAuthorize("@ss.hasPermi('biz:payInfo:add')")
    @Log(title = "收款信息", businessType = BusinessType.INSERT)
    @PostMapping("/biz/payInfo")
    public AjaxResult<String> add(@Valid @RequestBody ReceivePayInfoPostVo postVo) {
        return toAjax(receivePayInfoService.insertReceivePayInfo(ReceivePayInfoConvert.postVoToReceivePayInfo(postVo)));
    }

    @ApiOperation("修改收款信息")
    @PreAuthorize("@ss.hasPermi('biz:payInfo:edit')")
    @Log(title = "收款信息", businessType = BusinessType.UPDATE)
    @PutMapping("/biz/payInfo/{id}")
    public AjaxResult<String> edit(@PathVariable("id") Long id,
                                   @Valid @RequestBody ReceivePayInfoPutVo putVo) {
        return toAjax(receivePayInfoService.updateReceivePayInfo(ReceivePayInfoConvert.putVoToReceivePayInfo(id, putVo)));
    }

    @ApiOperation("删除收款信息")
    @PreAuthorize("@ss.hasPermi('biz:payInfo:remove')")
    @Log(title = "收款信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/biz/payInfo/{ids}")
    public AjaxResult<String> remove(@PathVariable Long[] ids) {
        return toAjax(receivePayInfoService.deleteReceivePayInfoByIds(ids));
    }
}
