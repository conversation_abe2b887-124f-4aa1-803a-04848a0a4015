package com.ali.biz.vo.putvo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 贷款信息 请求 报文
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@ApiModel(value = "贷款信息 修改参数")
@Data
public class LoanInfoPutVo {

    @ApiModelProperty(name = "loanUserId", value = "用户ID")
    private Long loanUserId;

    @ApiModelProperty(name = "loanAmount", value = "贷款金额")
    private BigDecimal loanAmount;

    @NotNull(message = "贷款日期 不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "startTime", value = "贷款日期")
    private Date startTime;

    @NotNull(message = "还款日期 不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "endTime", value = "还款日期")
    private Date endTime;

    @ApiModelProperty(name = "expectAmount", value = "逾期金额")
    private BigDecimal expectAmount;

    @ApiModelProperty(name = "payableAmount", value = "应还金额")
    private BigDecimal payableAmount;

    @Size(max = 1, message = "状态（1贷款中 2已还款） 不能超过 1 个字符")
    @ApiModelProperty(name = "status", value = "状态（1贷款中 2已还款）", allowableValues = "最大长度1")
    private String status;

    @Size(max = 500, message = "备注 不能超过 500 个字符")
    @ApiModelProperty(name = "remark", value = "备注", allowableValues = "最大长度500")
    private String remark;

}
