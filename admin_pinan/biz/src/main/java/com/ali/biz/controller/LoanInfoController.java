package com.ali.biz.controller;

import com.ali.biz.convert.LoanInfoConvert;
import com.ali.biz.domain.LoanInfo;
import com.ali.biz.service.ILoanInfoService;
import com.ali.biz.vo.postvo.LoanInfoPostVo;
import com.ali.biz.vo.putvo.LoanInfoPutVo;
import com.ali.common.annotation.Log;
import com.ali.common.core.controller.BaseController;
import com.ali.common.core.domain.AjaxResult;
import com.ali.common.enums.BusinessType;
import com.ali.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 贷款信息Controller
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Api(tags = "贷款信息相关接口")
@Validated
@RestController
public class LoanInfoController extends BaseController {
    @Autowired
    private ILoanInfoService loanInfoService;

    @ApiOperation("查询贷款信息列表")
    @PreAuthorize("@ss.hasPermi('biz:loanInfo:list')")
    @GetMapping("/biz/loanInfo/list")
    public AjaxResult<LoanInfo> list(LoanInfo loanInfo) {
        startPage();
        return AjaxResult.getDataTable(loanInfoService.selectLoanInfoList(loanInfo));
    }

    @ApiOperation("导出贷款信息列表")
    @PreAuthorize("@ss.hasPermi('biz:loanInfo:export')")
    @Log(title = "贷款信息", businessType = BusinessType.EXPORT)
    @PostMapping("/biz/loanInfo/export")
    public void export(HttpServletResponse response, LoanInfo loanInfo) {
        List<LoanInfo> list = loanInfoService.selectLoanInfoList(loanInfo);
        ExcelUtil<LoanInfo> util = new ExcelUtil<>(LoanInfo.class);
        util.exportExcel(response, list, "贷款信息数据");
    }

    @ApiOperation("获取贷款信息详细信息")
    @PreAuthorize("@ss.hasPermi('biz:loanInfo:query')")
    @GetMapping(value = "/biz/loanInfo/{id}")
    public AjaxResult<LoanInfo> getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(loanInfoService.selectLoanInfoById(id));
    }

    @ApiOperation("新增贷款信息")
    @PreAuthorize("@ss.hasPermi('biz:loanInfo:add')")
    @Log(title = "贷款信息", businessType = BusinessType.INSERT)
    @PostMapping("/biz/loanInfo")
    public AjaxResult<String> add(@Valid @RequestBody LoanInfoPostVo postVo) {
        return toAjax(loanInfoService.insertLoanInfo(LoanInfoConvert.postVoToLoanInfo(postVo)));
    }

    @ApiOperation("修改贷款信息")
    @PreAuthorize("@ss.hasPermi('biz:loanInfo:edit')")
    @Log(title = "贷款信息", businessType = BusinessType.UPDATE)
    @PutMapping("/biz/loanInfo/{id}")
    public AjaxResult<String> edit(@PathVariable("id") Long id,
                                   @Valid @RequestBody LoanInfoPutVo putVo) {
        return toAjax(loanInfoService.updateLoanInfo(LoanInfoConvert.putVoToLoanInfo(id, putVo)));
    }

    @ApiOperation("删除贷款信息")
    @PreAuthorize("@ss.hasPermi('biz:loanInfo:remove')")
    @Log(title = "贷款信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/biz/loanInfo/{ids}")
    public AjaxResult<String> remove(@PathVariable Long[] ids) {
        return toAjax(loanInfoService.deleteLoanInfoByIds(ids));
    }
}
