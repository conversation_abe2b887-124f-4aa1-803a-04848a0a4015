package com.ali.biz.vo.postvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 收款信息 请求 报文
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@ApiModel(value = "收款信息 请求参数")
@Data
public class ReceivePayInfoPostVo {

    @ApiModelProperty(name = "loanUserId", value = "用户ID")
    private Long loanUserId;

    @NotBlank(message = "开户姓名不能为空")
    @Size(max = 30, message = "开户姓名不能超过 30 个字符")
    @ApiModelProperty(name = "openName", value = "开户姓名", allowableValues = "最大长度30")
    private String openName;

    @NotBlank(message = "开户银行 不能为空")
    @Size(max = 100, message = "开户银行 不能超过 100 个字符")
    @ApiModelProperty(name = "openBank", value = "开户银行", allowableValues = "最大长度30")
    private String openBank;

    @NotBlank(message = "银行卡号 不能为空")
    @Size(max = 60, message = "银行卡号 不能超过 60个字符")
    @ApiModelProperty(name = "bankNumber", value = "银行卡号", allowableValues = "最大长度30")
    private String bankNumber;

    @Size(max = 150, message = "开户地址 不能超过 150 个字符")
    @ApiModelProperty(name = "openAddress", value = "开户地址", allowableValues = "最大长度150")
    private String openAddress;

    @Size(max = 500, message = "图片信息 不能超过 500 个字符")
    @ApiModelProperty(name = "imageInfo", value = "图片信息", allowableValues = "最大长度500")
    private String imageInfo;

    @Size(max = 1, message = "状态（1贷款中 2已还款） 不能超过 1 个字符")
    @ApiModelProperty(name = "status", value = "状态（1贷款中 2已还款）", allowableValues = "最大长度1")
    private String status;

    @Size(max = 500, message = "备注 不能超过 500 个字符")
    @ApiModelProperty(name = "remark", value = "备注", allowableValues = "最大长度500")
    private String remark;

}
