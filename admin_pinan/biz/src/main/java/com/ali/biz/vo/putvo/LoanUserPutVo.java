package com.ali.biz.vo.putvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 贷款用户 请求 报文
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@ApiModel(value = "贷款用户 修改参数")
@Data
public class LoanUserPutVo {

    @NotBlank(message = "用户账号 不能为空")
    @Size(max = 30, message = "用户账号 不能超过 30 个字符")
    @ApiModelProperty(name = "userName", value = "用户账号", allowableValues = "最大长度30")
    private String userName;

    @Size(max = 100, message = "密码 不能超过 100 个字符")
    @ApiModelProperty(name = "password", value = "密码", allowableValues = "最大长度100")
    private String password;

    @Size(max = 11, message = "手机号码 不能超过 11 个字符")
    @ApiModelProperty(name = "phoneNumber", value = "手机号码", allowableValues = "最大长度11")
    private String phoneNumber;

    @Size(max = 20, message = "身份证号不能超过 20 个字符")
    @ApiModelProperty(name = "idCard", value = "省份证号码", allowableValues = "最大长度20")
    private String idCard;

    @NotBlank(message = "开户银行 不能为空")
    @Size(max = 30, message = "开户银行 不能超过 30 个字符")
    @ApiModelProperty(name = "openBank", value = "开户银行", allowableValues = "最大长度30")
    private String openBank;

    @NotBlank(message = "银行卡号 不能为空")
    @Size(max = 30, message = "银行卡号 不能超过 30 个字符")
    @ApiModelProperty(name = "bankNumber", value = "银行卡号", allowableValues = "最大长度30")
    private String bankNumber;

    @Size(max = 150, message = "开户地址 不能超过 150 个字符")
    @ApiModelProperty(name = "openAddress", value = "开户地址", allowableValues = "最大长度150")
    private String openAddress;

    @NotNull(message = "显示顺序 不能为空")
    @ApiModelProperty(name = "sort", value = "显示顺序")
    private Long sort;

    @Size(max = 1, message = "帐号状态（1正常 2停用） 不能超过 1 个字符")
    @ApiModelProperty(name = "status", value = "帐号状态（1正常 2停用）", allowableValues = "最大长度1")
    private String status;

    @Size(max = 2, message = "用户类型（00系统用户，1.商户） 不能超过 2 个字符")
    @ApiModelProperty(name = "userType", value = "用户类型（00系统用户，1.商户）", allowableValues = "最大长度2")
    private String userType;

    @Size(max = 128, message = "最后登录IP 不能超过 128 个字符")
    @ApiModelProperty(name = "loginIp", value = "最后登录IP", allowableValues = "最大长度128")
    private String loginIp;

    @ApiModelProperty(name = "loginDate", value = "最后登录时间")
    private Date loginDate;

    @Size(max = 500, message = "备注 不能超过 500 个字符")
    @ApiModelProperty(name = "remark", value = "备注", allowableValues = "最大长度500")
    private String remark;

}
