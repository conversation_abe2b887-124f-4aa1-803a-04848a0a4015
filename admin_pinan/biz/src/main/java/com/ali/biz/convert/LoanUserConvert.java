package com.ali.biz.convert;

import com.ali.biz.domain.LoanUser;
import com.ali.biz.vo.postvo.LoanUserPostVo;
import com.ali.biz.vo.putvo.LoanUserPutVo;
import org.springframework.util.ObjectUtils;


/**
 * 贷款用户 转换类
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
public class LoanUserConvert {
    /**
     * LoanUserPostVo 转换为 LoanUser
     */
    public static LoanUser postVoToLoanUser(LoanUserPostVo postVo) {
        LoanUser loanUser = new LoanUser();
        if (ObjectUtils.isEmpty(postVo)) {
            return loanUser;
        }
        loanUser.setUserName(postVo.getUserName());
        loanUser.setPassword(postVo.getPassword());
        loanUser.setPhoneNumber(postVo.getPhoneNumber());
        loanUser.setIdCard(postVo.getIdCard());
        loanUser.setOpenBank(postVo.getOpenBank());
        loanUser.setBankNumber(postVo.getBankNumber());
        loanUser.setOpenAddress(postVo.getOpenAddress());
        loanUser.setSort(postVo.getSort());
        loanUser.setStatus(postVo.getStatus());
        loanUser.setUserType(postVo.getUserType());
        loanUser.setLoginIp(postVo.getLoginIp());
        loanUser.setLoginDate(postVo.getLoginDate());
        loanUser.setRemark(postVo.getRemark());
        return loanUser;
    }

    /**
     * LoanUserPutVo 转换为 LoanUser
     */
    public static LoanUser putVoToLoanUser(Long id, LoanUserPutVo putVo) {
        LoanUser loanUser = new LoanUser();
        if (ObjectUtils.isEmpty(putVo)) {
            return loanUser;
        }
        loanUser.setId(id);
        loanUser.setUserName(putVo.getUserName());
        loanUser.setPassword(putVo.getPassword());
        loanUser.setPhoneNumber(putVo.getPhoneNumber());
        loanUser.setIdCard(putVo.getIdCard());
        loanUser.setOpenBank(putVo.getOpenBank());
        loanUser.setBankNumber(putVo.getBankNumber());
        loanUser.setOpenAddress(putVo.getOpenAddress());
        loanUser.setSort(putVo.getSort());
        loanUser.setStatus(putVo.getStatus());
        loanUser.setUserType(putVo.getUserType());
        loanUser.setLoginIp(putVo.getLoginIp());
        loanUser.setLoginDate(putVo.getLoginDate());
        loanUser.setRemark(putVo.getRemark());
        return loanUser;
    }

}
