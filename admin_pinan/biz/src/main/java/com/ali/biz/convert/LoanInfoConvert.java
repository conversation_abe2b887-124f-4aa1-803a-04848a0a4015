package com.ali.biz.convert;

import com.ali.biz.domain.LoanInfo;
import com.ali.biz.vo.postvo.LoanInfoPostVo;
import com.ali.biz.vo.putvo.LoanInfoPutVo;
import org.springframework.util.ObjectUtils;


/**
 * 贷款信息 转换类
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
public class LoanInfoConvert {
    /**
     * LoanInfoPostVo 转换为 LoanInfo
     */
    public static LoanInfo postVoToLoanInfo(LoanInfoPostVo postVo) {
        LoanInfo loanInfo = new LoanInfo();
        if (ObjectUtils.isEmpty(postVo)) {
            return loanInfo;
        }
        loanInfo.setLoanUserId(postVo.getLoanUserId());
        loanInfo.setLoanAmount(postVo.getLoanAmount());
        loanInfo.setStartTime(postVo.getStartTime());
        loanInfo.setEndTime(postVo.getEndTime());
        loanInfo.setExpectAmount(postVo.getExpectAmount());
        loanInfo.setPayableAmount(postVo.getPayableAmount());
        loanInfo.setStatus(postVo.getStatus());
        loanInfo.setRemark(postVo.getRemark());
        return loanInfo;
    }

    /**
     * LoanInfoPutVo 转换为 LoanInfo
     */
    public static LoanInfo putVoToLoanInfo(Long id, LoanInfoPutVo putVo) {
        LoanInfo loanInfo = new LoanInfo();
        if (ObjectUtils.isEmpty(putVo)) {
            return loanInfo;
        }
        loanInfo.setId(id);
        loanInfo.setLoanUserId(putVo.getLoanUserId());
        loanInfo.setLoanAmount(putVo.getLoanAmount());
        loanInfo.setStartTime(putVo.getStartTime());
        loanInfo.setEndTime(putVo.getEndTime());
        loanInfo.setExpectAmount(putVo.getExpectAmount());
        loanInfo.setPayableAmount(putVo.getPayableAmount());
        loanInfo.setStatus(putVo.getStatus());
        loanInfo.setRemark(putVo.getRemark());
        return loanInfo;
    }

}
