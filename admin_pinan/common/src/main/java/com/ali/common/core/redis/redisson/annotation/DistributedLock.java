package com.ali.common.core.redis.redisson.annotation;


import com.ali.common.core.redis.redisson.LockTypeEnum;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 标记注解：redis分布式锁.
 * <p>
 * 使用案例： @DistributedLock(name = "name", key = "#user.userId")
 *
 * <AUTHOR>
 * @date 2023/05/29 12:12
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface DistributedLock {

    /**
     * 锁名
     *
     * @return 默认空值
     */
    String name() default "";

    /**
     * 键值
     * 支持 SpEL 表达式，如：#param.id
     *
     * @return 解析 SpEl 的值或原文
     */
    String key() default "";

    /**
     * 锁类型，
     * 如：可重入锁、公平锁、读锁、写锁
     *
     * @return 默认可重入锁
     */
    LockTypeEnum lockType() default LockTypeEnum.REENTRANT;

    /**
     * 是否尝试加锁，
     * true-尝试加锁，等待 waitTime 时间，无论成功还是失败都会返回；
     * false-阻塞加锁，直到完成加锁才返回。
     *
     * @return true-尝试加锁，false-阻塞加锁
     */
    boolean tryLock() default true;

    /**
     * 尝试加锁，最多等待时间，时间单位以 unit 为准
     * 默认值（-1）：不等待
     *
     * @return 默认-1
     */
    long waitTime() default -1;

    /**
     * 加锁以后多久自动解锁，时间单位以 unit 为准（如果设置，则不会自动续期）
     * 默认值（-1）：默认30秒，Redisson自动续期
     * 其它值：按填写的值到期自动解锁，Redisson不会自动续期
     *
     * @return 默认-1
     */
    long leaseTime() default -1;

    /**
     * 时间单位
     *
     * @return 默认秒
     */
    TimeUnit unit() default TimeUnit.SECONDS;

    /**
     * 获取失败是否抛出异常.
     *
     * @return true-抛出，false-不抛出
     */
    boolean isThrowException() default false;


}
