package com.ali.common.core.redis.redisson.provider;

import com.ali.common.core.redis.redisson.LockInfoVo;
import com.ali.common.core.redis.redisson.annotation.DistributedLock;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 解析SpEL生成锁key.
 *
 * <AUTHOR>
 * @date 2023/05/29 12:12
 */
@Component
public class LockKeyProvider {

    /**
     * key前缀.
     */
    private static final String KEY_PREFIX = "lock";
    /**
     * key分隔符.
     */
    private static final String KEY_SEPARATOR = ":";

    private final ExpressionParser parser = new SpelExpressionParser();
    private final ParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();

    /**
     * 获取锁信息VO.
     *
     * @param joinPoint       切入点
     * @param distributedLock 分布式锁标记注解
     * @return
     */
    public LockInfoVo getInfoVo(JoinPoint joinPoint, DistributedLock distributedLock) {
        LockInfoVo lockInfoVo = new LockInfoVo();
        lockInfoVo.setType(distributedLock.lockType());
        lockInfoVo.setTryLock(distributedLock.tryLock());
        lockInfoVo.setWaitTime(distributedLock.waitTime());
        lockInfoVo.setLeaseTime(distributedLock.leaseTime());
        lockInfoVo.setUnit(distributedLock.unit());
        // 解析SpEL表达式
        if (StringUtils.isNotBlank(distributedLock.key())) {
            Object rootObject = joinPoint.getTarget();
            Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
            Object[] args = joinPoint.getArgs();
            EvaluationContext context = new MethodBasedEvaluationContext(rootObject, method, args, discoverer);
            Expression expression = parser.parseExpression(distributedLock.key());
            String value = expression.getValue(context, String.class);
            lockInfoVo.setName(StringUtils.joinWith(KEY_SEPARATOR, KEY_PREFIX, distributedLock.name(), value));
        } else {
            lockInfoVo.setName(StringUtils.joinWith(KEY_SEPARATOR, KEY_PREFIX, distributedLock.name()));
        }
        return lockInfoVo;
    }

}
