package com.ali.common.okhttp;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.Request.Builder;
import org.apache.commons.io.FileUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Author: ali
 * @Date: 2024-05-13 10:23
 * @Description:
 */
@Slf4j
public class OkHttpUtil {

    public static final String MEDIA_TYPE_JSON = "application/json; charset=utf-8";

    /**
     * 创建 单例的 OkHttpClient
     */
    private static final OkHttpClient okHttpClient = new OkHttpClient.Builder()
            .connectTimeout(120L, TimeUnit.SECONDS)
            .readTimeout(120L, TimeUnit.SECONDS)
            .writeTimeout(120L, TimeUnit.SECONDS)
//            .proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1", 8888)))
            .build();


    /**
     * 获取默认的headers
     *
     * @param headMap 请求头
     * @return Headers
     */
    private static Headers getHeaders(Map<String, String> headMap) {
        if (Objects.isNull(headMap))
            return new Headers.Builder().build();
        Headers.Builder headersBuilder = new Headers.Builder();
        for (Map.Entry<String, String> entry : headMap.entrySet()) {
            headersBuilder.add(entry.getKey(), entry.getValue());
        }
        return headersBuilder.build();
    }


    /**
     * GET请求。使用默认的 okHttpClient 和 headers
     *
     * @param url url
     * @return url
     */
    public static String get(String url) {
        return get(url, new HashMap<>());
    }

    /**
     * get请求
     *
     * @param url url
     * @return url
     */
    public static String get(String url, Map<String, String> heardMap) {
        Builder builder = new Builder().url(url).headers(getHeaders(heardMap)).get();
        String responseData = request(url, builder.build());
        log.info("okHttpClient get url:{},request responseData====> {}", url, responseData);
        return responseData;
    }

    /**
     * get请求
     *
     * @param url      请求url
     * @param paramMap 请求参数
     * @return 响应结果
     */
    public static String doGet(String url, Map<String, String> paramMap, Map<String, String> heardMap) {
        StringBuilder urlbuilder = new StringBuilder(url);
        if (Objects.nonNull(paramMap)) {
            urlbuilder.append("?");
            paramMap.forEach((key, value) -> {
                try {
                    urlbuilder.append(URLEncoder.encode(key, "UTF-8")).append("=")
                            .append(URLEncoder.encode(value, "UTF-8")).append("&");
                } catch (Exception e) {
                    log.error("doGet paramMap error.", e);
                }
            });
            urlbuilder.deleteCharAt(urlbuilder.length() - 1);
        }
        return get(urlbuilder.toString(), heardMap);
    }

    /**
     * 没有请求头
     *
     * @param url      url
     * @param paramMap 请求参数
     * @return 响应结果
     */
    public static String doGet(String url, Map<String, String> paramMap) {
        return doGet(url, paramMap, null);
    }

    /**
     * 没有请求头 没有请求参数
     *
     * @param url url
     * @return 响应结果
     */
    public static String doGet(String url) {
        return doGet(url, null, null);
    }


    /**
     * post请求，获取响应结果
     *
     * @param url      请求url
     * @param bodyJson 请求体
     * @param heardMap 请求头
     * @return bodyJson
     */
    public static String post(String url, JSONObject bodyJson, Map<String, String> heardMap) {
        MediaType mediaTypeJson = MediaType.parse(MEDIA_TYPE_JSON);
        RequestBody requestBody = RequestBody.create(mediaTypeJson, JSON.toJSONString(bodyJson));
        Request request = new Builder().url(url).headers(getHeaders(heardMap)).post(requestBody).build();

        String responseData = request(url, request);
        log.info("okHttpClient post url:{},post responseData====> {}", url, responseData);
        return responseData;
    }

    /**
     * post请求。使用默认的 okHttpClient 和 headers
     *
     * @param url      url
     * @param bodyJson bodyJson
     * @return bodyJson
     */
    public static String post(String url, JSONObject bodyJson) {
        //如果需要自定义 okHttpClient或headers传参，可以调用以下方法
        return post(url, bodyJson, null);
    }

    /**
     * 获取响应结果
     *
     * @param url
     * @param request
     * @return
     */
    public static String request(String url, Request request) {
        String responseData = "";
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.body() != null)
                return response.body().string();
        } catch (Exception e) {
            log.error("okHttpClient getResponse error.url:{}", url, e);
        }
        return responseData;
    }

    /**
     * 上传文件
     *
     * @param url          上传文件的url
     * @param fileKey      文件对应的key
     * @param formDataJson form-data参数
     * @param headerMap    请求头
     * @param file         文件
     * @return String
     */
    public static String uploadFile(String url, String fileKey, File file, JSONObject formDataJson, HashMap<String, String> headerMap) {
        log.info("uploadFile url:{}, uploadFile formDataJson====> {}", url, formDataJson);
        // 支持传文件的同时，传参数。
        MultipartBody requestBody = getMultipartBody(fileKey, file, formDataJson);
        // 构建request请求体
        Request request = new Builder().url(url).headers(getHeaders(headerMap)).post(requestBody).build();
        String responseData = request(url, request);
        // 会在本地产生临时文件，用完后需要删除
        if (file.exists()) {
            file.delete();
        }
        return responseData;

    }

    /**
     * 上传文件
     *
     * @param url           String
     * @param fileKey       form-data文件对应的key
     * @param multipartFile 文件上传对应的 multipartFile
     * @param formDataJson  form-data参数
     * @return String
     */
    public static String uploadFile(String url, String fileKey, MultipartFile multipartFile, JSONObject formDataJson) {
        return uploadFile(url, fileKey, getFile(multipartFile), formDataJson, null);
    }


    /**
     * 上传文件
     * 使用默认的okHttpClient
     *
     * @param url
     * @param fileKey      form-data文件对应的key
     * @param file         文件
     * @param formDataJson form-data参数
     * @return String
     */
    public static String uploadFile(String url, String fileKey, File file, JSONObject formDataJson) {
        return uploadFile(url, fileKey, file, formDataJson, null);
    }

    /**
     * 上传文件用。构建form-data 参数
     *
     * @param fileKey      文件对应的key
     * @param file         文件
     * @param formDataJson form-data参数
     * @return MultipartBody
     */
    public static MultipartBody getMultipartBody(String fileKey, File file, JSONObject formDataJson) {
        RequestBody fileBody = RequestBody.create(MultipartBody.FORM, file);

        MultipartBody.Builder bodyBuilder = new MultipartBody.Builder();
        // 设置传参为form-data格式
        bodyBuilder.setType(MultipartBody.FORM);
        bodyBuilder.addFormDataPart(fileKey, file.getName(), fileBody);
        // 添加 form-data参数
        for (Map.Entry<String, Object> entry : formDataJson.entrySet()) {
            //参数通过 bodyBuilder.addFormDataPart(key, value) 添加
            bodyBuilder.addFormDataPart(entry.getKey(), Objects.toString(entry.getValue(), ""));
        }
        return bodyBuilder.build();
    }

    /**
     * 获取文件
     *
     * @param multipartFile multipartFile
     * @return multipartFile
     */
    public static File getFile(MultipartFile multipartFile) {
        File file = new File(Objects.requireNonNull(multipartFile.getOriginalFilename()));
        try {
            FileUtils.copyInputStreamToFile(multipartFile.getInputStream(), file);
        } catch (IOException e) {
            log.error("copyInputStreamToFile error.", e);
        }
        return file;
    }

}


