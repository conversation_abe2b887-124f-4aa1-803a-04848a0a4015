package com.ali.common.core.domain;

import com.ali.common.enums.RestResponseCode;
import com.ali.common.utils.StringUtils;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 操作消息提醒
 *
 * <AUTHOR>
 */
@Data
@Api(value = "AjaxResult", description = "响应数据")
public final class AjaxResult<T> implements Serializable {
    /**
     * 数据对象
     */
    public static final String DATA_TAG = "data";
    private static final long serialVersionUID = 1L;
    /**
     * 状态码
     */
    private static final String CODE_TAG = "code";
    /**
     * 返回内容
     */
    private static final String MSG_TAG = "msg";
    /**
     * code 状态码: 200 正常，300 异常，400 重定向，500 错误
     */
    @ApiModelProperty(value = "状态码: 200 正常，300 异常，400 重定向，500 错误,401 未授权, 403 没有权限", name = "code", example = "200")
    private int code;
    /**
     * msg 提示信息
     */
    @ApiModelProperty(value = "提示信息", name = "msg", example = "成功")
    private String msg;
    /**
     * data 返回数据
     */
    @ApiModelProperty(value = "返回数据", name = "data", example = "1")
    private T data;
    @ApiModelProperty(value = "总记录数,只有分页查询才有值", name = "total", example = "200")
    private long total;

    /**
     * 初始化一个新创建的 AjaxResult 对象，使其表示一个空消息。
     */
    private AjaxResult() {
    }

    /**
     * 初始化一个新创建的 AjaxResult 对象
     *
     * @param code 状态码
     * @param msg  返回内容
     */
    private AjaxResult(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * 初始化一个新创建的 AjaxResult 对象
     *
     * @param code 状态码
     * @param msg  返回内容
     * @param data 数据对象
     */
    private AjaxResult(int code, String msg, T data) {
        this(code, msg);
        if (StringUtils.isNotNull(data)) {
            this.data = data;
        }
    }

    /**
     * 初始化一个新创建的 AjaxResult 对象
     *
     * @param responseCode 相应状态码
     * @param data         数据对象
     */
    private AjaxResult(RestResponseCode responseCode, T data, long total) {
        this(responseCode.getCode(), responseCode.getMsg());
        this.data = data;
        this.total = total;
    }


    /**
     * 返回成功消息
     *
     * @return 成功消息
     */
    public static <T> AjaxResult<T> success() {
        return AjaxResult.success(RestResponseCode.SUCCESS);
    }

    /**
     * 返回成功数据
     *
     * @return 成功消息
     */
    public static <T> AjaxResult<T> success(T data) {
        return AjaxResult.success(RestResponseCode.SUCCESS, data);
    }


    /**
     * 返回错误消息
     *
     * @return 错误消息
     */
    public static <T> AjaxResult<T> error() {
        return AjaxResult.error(RestResponseCode.ERROR);
    }

    /**
     * 返回错误消息
     *
     * @param msg 返回内容
     * @return 错误消息
     */
    public static <T> AjaxResult<T> error(String msg) {
        return AjaxResult.error(msg, null);
    }

    /**
     * 返回错误消息
     *
     * @param msg  返回内容
     * @param data 数据对象
     * @return 错误消息
     */
    public static <T> AjaxResult<T> error(String msg, T data) {
        return new AjaxResult(RestResponseCode.ERROR.getCode(), msg, data);
    }

    /**
     * 返回错误消息
     *
     * @param code 返回内容
     * @return 错误消息
     */
    public static <T> AjaxResult<T> error(RestResponseCode code) {
        return AjaxResult.error(code.getCode(), code.getMsg());
    }

    /**
     * 返回错误消息
     *
     * @param code 状态码
     * @param msg  返回内容
     * @return 错误消息
     */
    public static <T> AjaxResult<T> error(int code, String msg) {
        return new AjaxResult(code, msg, null);
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    public static <T> AjaxResult<T> toAjax(int rows) {
        return rows > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    public static <T> AjaxResult<T> toAjax(boolean result) {
        return result ? success() : error();
    }

    /**
     * 返回警告消息
     *
     * @param msg 返回内容
     * @return 警告消息
     */
    public static <T> AjaxResult<T> warn(String msg) {
        return AjaxResult.warn(msg, null);
    }


    /**
     * 返回警告消息
     *
     * @param msg  返回内容
     * @param data 数据对象
     * @return 警告消息
     */
    public static <T> AjaxResult<T> warn(String msg, T data) {
        return new AjaxResult(RestResponseCode.WARNING.getCode(), msg, data);
    }

    /**
     * 分页参数
     *
     * @param list
     * @param <T>
     * @return
     */
    public static <T> AjaxResult<T> getDataTable(List<T> list) {
        return new AjaxResult(RestResponseCode.SUCCESS, list, new PageInfo(list).getTotal());
    }

    /**
     * 返回成功消息
     *
     * @param code 返回内容
     * @return 成功消息
     */
    private static <T> AjaxResult<T> success(RestResponseCode code) {
        return AjaxResult.success(code, null);
    }

    /**
     * 返回成功消息
     *
     * @param code 返回内容
     * @param data 数据对象
     * @return 成功消息
     */
    private static <T> AjaxResult<T> success(RestResponseCode code, T data) {
        return new AjaxResult(code.getCode(), code.getMsg(), data);
    }

}
