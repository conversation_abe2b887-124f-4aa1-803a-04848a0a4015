package com.ali.common.core.redis.redisson;

import lombok.Data;

import java.util.concurrent.TimeUnit;

/**
 * 锁信息vo.
 *
 * <AUTHOR>
 * @date 2023/05/29 12:12
 */
@Data
public class LockInfoVo {

    /**
     * 锁名称.
     */
    private String name;
    /**
     * 锁类型.
     */
    private LockTypeEnum type;
    /**
     * 是否尝试加锁，
     * true-尝试加锁，等待 waitTime 时间，无论成功还是失败都会返回；
     * false-阻塞加锁，直到完成加锁才返回。
     */
    private boolean tryLock;
    /**
     * 尝试加锁，最多等待时间，时间单位以 unit 为准
     */
    private long waitTime;
    /**
     * 加锁以后多久自动解锁，时间单位以 unit 为准
     */
    private long leaseTime;
    /**
     * 时间单位
     */
    private TimeUnit unit;

}
