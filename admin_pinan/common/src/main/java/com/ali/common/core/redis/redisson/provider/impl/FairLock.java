package com.ali.common.core.redis.redisson.provider.impl;

import com.ali.common.core.redis.redisson.LockInfoVo;
import com.ali.common.core.redis.redisson.provider.AbstractLock;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

/**
 * 公平锁.
 *
 * <AUTHOR>
 * @date 2023/05/29 12:12
 */
public class FairLock extends AbstractLock {

    public FairLock(RedissonClient redissonClient, LockInfoVo lockInfoVo) {
        super(redissonClient, lockInfoVo);
    }

    @Override
    protected RLock getRedissonLock() {
        return getRedissonClient().getFairLock(getLockInfoVo().getName());
    }
}
