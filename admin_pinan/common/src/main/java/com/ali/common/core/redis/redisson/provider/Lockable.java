package com.ali.common.core.redis.redisson.provider;

import com.ali.common.core.redis.redisson.LockInfoVo;

/**
 * 锁接口.
 *
 * <AUTHOR>
 * @date 2023/05/29 12:12
 */
public interface Lockable {

    /**
     * 加锁
     *
     * @return true-加锁成功；false-加锁失败
     */
    boolean lock();

    /**
     * 解锁
     */
    void unlock();

    /**
     * 获取锁信息
     *
     * @return 锁信息
     */
    LockInfoVo getLockInfoVo();
}
