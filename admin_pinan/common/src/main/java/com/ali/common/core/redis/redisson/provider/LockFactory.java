package com.ali.common.core.redis.redisson.provider;

import com.ali.common.core.redis.redisson.LockInfoVo;
import com.ali.common.core.redis.redisson.annotation.DistributedLock;
import com.ali.common.core.redis.redisson.provider.impl.FairLock;
import com.ali.common.core.redis.redisson.provider.impl.ReentrantLock;
import com.ali.common.core.redis.redisson.provider.impl.WriteLock;
import org.aspectj.lang.JoinPoint;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 锁工厂.
 *
 * <AUTHOR>
 * @date 2023/05/29 12:12
 */
@Component
public class LockFactory {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private LockKeyProvider lockKeyProvider;

    public Lockable getLock(JoinPoint joinPoint, DistributedLock distributedLock) {
        LockInfoVo lockInfoVo = lockKeyProvider.getInfoVo(joinPoint, distributedLock);
        switch (lockInfoVo.getType()) {
            case REENTRANT:
                return new ReentrantLock(redissonClient, lockInfoVo);
            case FAIR:
                return new FairLock(redissonClient, lockInfoVo);
            case WRITE:
                return new WriteLock(redissonClient, lockInfoVo);
            default:
                throw new UnsupportedOperationException("还未实现该类型的锁");
        }
    }
}
