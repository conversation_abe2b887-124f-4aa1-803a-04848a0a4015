package com.ali.common.enums;

/**
 * 自定义响应状态码枚举
 *
 * <AUTHOR>
 * @date 2023-08-26：17:03
 */
public enum RestResponseCode {
    SUCCESS(200, "success"),
    FAIL(300, "fail"),
    BAD_REQUEST(303, "重定向！"),
    BODY_NOT_MATCH(400, "请求的数据格式不符！"),
    UNAUTHORIZED(401, "未授权！"),
    NOT_LOGIN(402, "未登录或登录已过期！"),
    FORBIDDEN(403, "没有权限，请联系管理员授权！"),
    NOT_FOUND(404, "未找到该资源！"),
    BAD_METHOD(405, "请求方式不支持！"),
    CONFLICT(409, "资源冲突！"),
    UNSUPPORTED_TYPE(415, "不支持当前媒体类型！"),
    ERROR(500, "服务器内部错误！"),
    NOT_IMPLEMENTED(501, "服务器不支持当前请求所需要的某个功能！"),
    SERVER_BUSY(503, "服务器正忙，请稍后再试！"),
    WARNING(601, "警告！"),
    UNDEFINED_ERROR(1000, "未知错误！"),
    NOTE(1001, "自定义提示！"),
    PARAMETERERROR(409, "参数错误！"),
    NOTLOGIN(402, "登陆错误！"),
    EXPIRE(1002, "Session过期了");
    int code;
    String msg;

    RestResponseCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
