package com.ali.common.utils.ip;

import com.ali.common.utils.StringUtils;
import net.dreamlu.mica.ip2region.core.Ip2regionSearcher;
import net.dreamlu.mica.ip2region.core.IpInfo;

/**
 * @Author: ali
 * @Date: 2024-07-23 16:34
 * @Description: 获取IP 信息
 */
public class Ip2regionUtils {
    /**
     * 获取 IP 信心
     *
     * @param ip                IP
     * @param ip2regionSearcher ip2regionSearcher
     * @return String
     */
    public static String getLoginLocation(String ip, Ip2regionSearcher ip2regionSearcher) {
        if (StringUtils.isEmpty(ip))
            return "";
        IpInfo info = ip2regionSearcher.memorySearch(ip);
        if (null == info)
            return "";
        String str = "";
        if (StringUtils.isNotEmpty(info.getCountry()))
            str += info.getCountry();

        if (StringUtils.isNotEmpty(info.getProvince()))
            str += info.getProvince();

        if (StringUtils.isNotEmpty(info.getCity()))
            str += info.getCity();

        if (StringUtils.isNotEmpty(info.getIsp()) && !info.getIsp().equals("内网IP"))
            str += "(" + info.getIsp() + ")";

        return str;
    }
}
