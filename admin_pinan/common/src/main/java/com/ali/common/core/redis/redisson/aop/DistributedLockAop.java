package com.ali.common.core.redis.redisson.aop;

import com.ali.common.core.redis.redisson.annotation.DistributedLock;
import com.ali.common.core.redis.redisson.provider.LockFactory;
import com.ali.common.core.redis.redisson.provider.Lockable;
import com.ali.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 分布式锁Aop处理.
 *
 * <AUTHOR>
 * @date 2023/05/29 12:12
 */
@Slf4j
@Aspect
@Component
@Order(0)
public class DistributedLockAop {

    /**
     * 锁工厂.
     */
    @Autowired
    private LockFactory lockFactory;

    /**
     * 分布式锁环绕通知.
     *
     * @param joinPoint       横切点
     * @param distributedLock 分布式锁标记注解
     */
    @Around("@annotation(distributedLock)")
    public Object around(ProceedingJoinPoint joinPoint, DistributedLock distributedLock) throws Throwable {
        Lockable lock = lockFactory.getLock(joinPoint, distributedLock);
        if (!lock.lock()) {
            // 加锁失败，是否抛出异常
            if (distributedLock.isThrowException()) {
                throw new ServiceException(String.format("加锁失败！加锁[%s]失败", lock.getLockInfoVo().getName()));
            } else {
                return null;
            }
        }
        log.info("加锁成功！加锁[{}]成功", lock.getLockInfoVo().getName());
        try {
            return joinPoint.proceed();
        } finally {
            try {
                lock.unlock();
                log.info("解锁成功！解锁[{}]成功", lock.getLockInfoVo().getName());
            } catch (Exception e) {
                log.error("解锁失败！解锁[{}]失败", lock.getLockInfoVo().getName(), e);
            }
        }
    }
}
