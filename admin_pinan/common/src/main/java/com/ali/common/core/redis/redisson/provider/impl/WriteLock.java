package com.ali.common.core.redis.redisson.provider.impl;


import com.ali.common.core.redis.redisson.LockInfoVo;
import com.ali.common.core.redis.redisson.provider.AbstractLock;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

/**
 * 写锁.
 *
 * <AUTHOR>
 * @date 2023/05/29 12:12
 */
@Slf4j
public class WriteLock extends AbstractLock {
    public WriteLock(RedissonClient redissonClient, LockInfoVo lockInfoVo) {
        super(redissonClient, lockInfoVo);
    }

    @Override
    protected RLock getRedissonLock() {
        return getRedissonClient().getReadWriteLock(getLockInfoVo().getName()).writeLock();
    }
}
