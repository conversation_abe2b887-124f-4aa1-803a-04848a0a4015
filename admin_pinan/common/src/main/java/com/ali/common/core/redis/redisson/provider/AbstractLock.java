package com.ali.common.core.redis.redisson.provider;

import com.ali.common.core.redis.redisson.LockInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.util.Assert;

/**
 * 抽象锁.
 *
 * <AUTHOR>
 * @date 2023/05/29 12:12
 */
@Slf4j
public abstract class AbstractLock implements Lockable {

    private final RedissonClient redissonClient;
    private final LockInfoVo lockInfoVo;
    private RLock rLock;

    protected AbstractLock(RedissonClient redissonClient, LockInfoVo lockInfoVo) {
        this.redissonClient = redissonClient;
        this.lockInfoVo = lockInfoVo;
    }

    @Override
    public boolean lock() {
        try {
            rLock = getRedissonLock();
            if (getLockInfoVo().isTryLock()) {
                return rLock.tryLock(getLockInfoVo().getWaitTime(), getLockInfoVo().getLeaseTime(), getLockInfoVo().getUnit());
            } else {
                rLock.lock(getLockInfoVo().getLeaseTime(), getLockInfoVo().getUnit());
                return true;
            }
        } catch (InterruptedException e) {
            log.error("redisson加锁失败", e);
            Thread.currentThread().interrupt();
            return false;
        }
    }

    @Override
    public void unlock() {
        Assert.notNull(rLock, "锁为空，未能执行解锁！");
        rLock.unlock();
    }

    @Override
    public LockInfoVo getLockInfoVo() {
        return lockInfoVo;
    }

    /**
     * 获取 Redisson Lock
     *
     * @return RLock
     */
    protected abstract RLock getRedissonLock();

    protected RedissonClient getRedissonClient() {
        return redissonClient;
    }
}
