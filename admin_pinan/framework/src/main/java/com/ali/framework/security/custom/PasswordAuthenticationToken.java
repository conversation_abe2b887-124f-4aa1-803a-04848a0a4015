package com.ali.framework.security.custom;

import lombok.Getter;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;

/**
 * @Author: ali
 * @Date: 2025-08-11 15:29
 * @Description:
 */
@Getter
public class PasswordAuthenticationToken extends UsernamePasswordAuthenticationToken {
    private final String userType;

    public PasswordAuthenticationToken(Object principal, Object credentials, String userType) {
        super(principal, credentials);
        this.userType = userType;
    }

}
