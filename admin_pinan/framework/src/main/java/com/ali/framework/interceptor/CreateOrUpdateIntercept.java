package com.ali.framework.interceptor;

import com.ali.common.utils.DateUtils;
import com.ali.common.utils.SecurityUtils;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.springframework.data.util.ReflectionUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

/**
 * 新增修改增加默认值
 */
@Component
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
public class CreateOrUpdateIntercept implements Interceptor {

    private final static String CREATE_FIELD = "createBy";

    private final static String CREATE_TIME_FIELD = "createTime";

    private final static String UPDATE_FIELD = "updateBy";

    private final static String UPDATE_TIME_FIELD = "updateTime";

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement ms = (MappedStatement) invocation.getArgs()[0];

        Object parameter = invocation.getArgs()[1];

        SqlCommandType sqlCommandType = ms.getSqlCommandType();
        if (!parameter.getClass().getSimpleName().toLowerCase().startsWith("sys")) {
            if (SqlCommandType.INSERT.equals(sqlCommandType)) {
                Field fieldCreate = this.getField(parameter.getClass(), CREATE_FIELD);
                if (null != fieldCreate && null == fieldCreate.get(parameter)) {
                    ReflectionUtils.setField(fieldCreate, parameter, SecurityUtils.getUsername());
                }
                Field fieldCreateTime = this.getField(parameter.getClass(), CREATE_TIME_FIELD);
                if (null != fieldCreateTime) {
                    ReflectionUtils.setField(fieldCreateTime, parameter, DateUtils.getNowDate());
                }
            } else if (SqlCommandType.UPDATE.equals(sqlCommandType)) {

                Field fieldUpdate = this.getField(parameter.getClass(), UPDATE_FIELD);
                if (null != fieldUpdate && null == fieldUpdate.get(parameter)) {
                    ReflectionUtils.setField(fieldUpdate, parameter, SecurityUtils.getUsername());
                }
                Field fieldUpdateTime = this.getField(parameter.getClass(), UPDATE_TIME_FIELD);
                if (null != fieldUpdateTime) {
                    ReflectionUtils.setField(fieldUpdateTime, parameter, DateUtils.getNowDate());
                }
            }
        }
        return invocation.proceed();
    }

    private Field getField(Class<?> className, String attr) throws SecurityException {
        Field field = null;
        try {
            field = className.getSuperclass().getDeclaredField(attr);
            field.setAccessible(true);
        } catch (NoSuchFieldException e) {
            return field;
        }
        return field;
    }

    @SuppressWarnings({"rawtypes", "unused"})
    private boolean hasField(Class c, String fieldName) {
        Field[] fields = c.getDeclaredFields();
        for (Field f : fields) {
            if (fieldName.equals(f.getName())) {
                return true;
            }
        }
        return false;
    }

    @SuppressWarnings("unused")
    private Field getFieldSelf(Class<?> className, String attr) throws SecurityException {
        Field field = null;
        try {
            field = className.getDeclaredField(attr);
            field.setAccessible(true);
        } catch (NoSuchFieldException e) {
            return field;
        }
        return field;
    }
}
