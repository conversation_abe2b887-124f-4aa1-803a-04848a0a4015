package com.ali.framework.web.service;

import com.ali.biz.domain.LoanUser;
import com.ali.biz.service.ILoanUserService;
import com.ali.common.constant.CacheConstants;
import com.ali.common.constant.Constants;
import com.ali.common.constant.UserConstants;
import com.ali.common.core.domain.entity.SysUser;
import com.ali.common.core.domain.model.LoginUser;
import com.ali.common.core.redis.RedisCache;
import com.ali.common.exception.ServiceException;
import com.ali.common.exception.user.*;
import com.ali.common.utils.DateUtils;
import com.ali.common.utils.GoogleAuthenticator;
import com.ali.common.utils.MessageUtils;
import com.ali.common.utils.StringUtils;
import com.ali.common.utils.ip.Ip2regionUtils;
import com.ali.common.utils.ip.IpUtils;
import com.ali.framework.manager.AsyncManager;
import com.ali.framework.manager.factory.AsyncFactory;
import com.ali.framework.security.context.AuthenticationContextHolder;
import com.ali.framework.security.custom.PasswordAuthenticationToken;
import com.ali.system.service.ISysConfigService;
import com.ali.system.service.ISysUserService;
import net.dreamlu.mica.ip2region.core.Ip2regionSearcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private Ip2regionSearcher ip2regionSearcher;

    @Autowired
    private ILoanUserService loanUserService;

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid, String userType) {
        if ("admin".equals(userType))
            // 验证码校验
            validateCaptcha(username, code, uuid);
        // 登录前置校验
        loginPreCheck(username, password);
        // 用户验证
//        checkGoogleAuth(username, code);
        // 用户验证
        Authentication authentication = null;

        try {
            PasswordAuthenticationToken authenticationToken = new PasswordAuthenticationToken(username, password, userType);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId(), userType);
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid) {
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled) {
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
            String captcha = redisCache.getCacheObject(verifyKey);
            if (captcha == null) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
                throw new CaptchaExpireException();
            }
            redisCache.deleteObject(verifyKey);
            if (!code.equalsIgnoreCase(captcha)) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
                throw new CaptchaException();
            }
        }
    }

    /**
     * 登录前置校验
     *
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // IP黑名单校验
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
            throw new BlackListException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId, String userType) {
        if ("admin".equals(userType)) {
            SysUser sysUser = new SysUser();
            sysUser.setUserId(userId);
            if (StringUtils.isNotEmpty(IpUtils.getIpAddr()))
                sysUser.setLoginIp(Ip2regionUtils.getLoginLocation(IpUtils.getIpAddr(), ip2regionSearcher) + "|" + IpUtils.getIpAddr());
            sysUser.setLoginDate(DateUtils.getNowDate());
            userService.updateUserProfile(sysUser);
        } else {
            LoanUser loanUser = new LoanUser();
            loanUser.setId(userId);
            if (StringUtils.isNotEmpty(IpUtils.getIpAddr())) {
                loanUser.setLoginIp(Ip2regionUtils.getLoginLocation(IpUtils.getIpAddr(), ip2regionSearcher) + "|" + IpUtils.getIpAddr());
            }
            loanUser.setLoginDate(DateUtils.getNowDate());
            loanUserService.updateLoanUser(loanUser);
        }
    }

    /**
     * 谷歌验证码校验
     *
     * @param googleCode
     */
    private void checkGoogleAuth(String userName, String googleCode) {
        SysUser user = userService.selectUserByUserName(userName);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }
        if (StringUtils.isEmpty(user.getGoogleToken())) {
            return;
        }
        if (StringUtils.isEmpty(googleCode)) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(user.getUserName(), Constants.LOGIN_FAIL, "谷歌验证码不能为空"));
            throw new ServiceException("谷歌验证码不能为空");
        }
        if (!GoogleAuthenticator.checkByThrowException(Long.valueOf(googleCode), user.getGoogleToken())) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(user.getUserName(), Constants.LOGIN_FAIL, "谷歌验证码错误"));
            throw new ServiceException("谷歌验证码错误");
        }
    }
}
