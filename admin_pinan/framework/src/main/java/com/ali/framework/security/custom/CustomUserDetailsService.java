package com.ali.framework.security.custom;

import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;

/**
 * @Author: ali
 * @Date: 2025-08-11 15:30
 * @Description:
 */
@Service
public interface CustomUserDetailsService extends UserDetailsService {
    UserDetails loadUserByUsernameAndType(String username, String userType);
}