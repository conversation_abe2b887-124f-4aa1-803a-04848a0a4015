package com.ali.framework.security.custom;

import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * @Author: ali
 * @Date: 2025-08-11 15:30
 * @Description:
 */
public class CustomAuthenticationProvider extends DaoAuthenticationProvider {

    private final CustomUserDetailsService userDetailsService;
    private final PasswordEncoder passwordEncoder;

    public CustomAuthenticationProvider(CustomUserDetailsService userDetailsService, PasswordEncoder passwordEncoder) {
        if (userDetailsService == null) {
            throw new IllegalArgumentException("A UserDetailsService must be set");
        }
        this.userDetailsService = userDetailsService;
        this.passwordEncoder = passwordEncoder;
    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        if (!(authentication instanceof PasswordAuthenticationToken)) {
            return null;
        }
        PasswordAuthenticationToken token = (PasswordAuthenticationToken) authentication;
        String username = token.getName();
        String password = token.getCredentials().toString();
        String userType = token.getUserType();

        UserDetails userDetails = userDetailsService.loadUserByUsernameAndType(username, userType);

        if (!passwordEncoder.matches(password, userDetails.getPassword())) {
            throw new BadCredentialsException("密码错误");
        }

        // 认证成功，返回一个认证过的Token（可以用 UsernamePasswordAuthenticationToken）
        return new PasswordAuthenticationToken(userDetails, userDetails.getPassword(), userType);
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return PasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }
}