package com.ali.framework.security.custom;

import com.ali.biz.domain.LoanUser;
import com.ali.biz.service.ILoanUserService;
import com.ali.common.core.domain.entity.SysUser;
import com.ali.common.core.domain.model.LoginUser;
import com.ali.common.enums.UserStatus;
import com.ali.common.exception.ServiceException;
import com.ali.common.utils.MessageUtils;
import com.ali.common.utils.StringUtils;
import com.ali.framework.web.service.SysPasswordService;
import com.ali.framework.web.service.SysPermissionService;
import com.ali.system.service.ISysUserService;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Set;

/**
 * @Author: ali
 * @Date: 2025-08-11 15:31
 * @Description:
 */
@Service
public class CustomUserDetailsServiceImpl implements CustomUserDetailsService {
    private static final Logger log = LoggerFactory.getLogger(CustomUserDetailsServiceImpl.class);

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private ILoanUserService loanUserService;

    @Override
    public UserDetails loadUserByUsernameAndType(String username, String userType) throws UsernameNotFoundException {
        // 根据 userType 决定查哪个表
        if ("admin".equals(userType)) {
            SysUser user = userService.selectUserByUserName(username);
            if (StringUtils.isNull(user)) {
                log.info("登录用户：{} 不存在.", username);
                throw new ServiceException(MessageUtils.message("user.not.exists"));
            } else if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
                log.info("登录用户：{} 已被删除.", username);
                throw new ServiceException(MessageUtils.message("user.password.delete"));
            } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
                log.info("登录用户：{} 已被停用.", username);
                throw new ServiceException(MessageUtils.message("user.blocked"));
            }
            passwordService.validate(user);
            return createLoginUser(user, permissionService.getMenuPermission(user));
        }
        // 代理用户
        LoanUser loanUser = loanUserService.selectLoanUserByPhone(username);
        if (ObjectUtils.isEmpty(loanUser) || UserStatus.DELETED.getCode().equals(loanUser.getStatus())) {
            log.info("登录用户：{} 不存在.", username);
            throw new ServiceException(MessageUtils.message("user.not.exists"));
        }
        SysUser user = new SysUser();
        user.setUserId(loanUser.getId());
        user.setUserName(username);
        user.setPassword(loanUser.getPassword());
        user.setRoleIds(new Long[]{3L});
        user.setUserType("00");
        passwordService.validate(user);
        return createLoginUser(user, Collections.singleton("*:*:*"));
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        throw new UnsupportedOperationException("请使用 loadUserByUsernameAndType 方法");
    }

    public UserDetails createLoginUser(SysUser user, Set<String> perms) {
        return new LoginUser(user.getUserId(), user.getDeptId(), user, perms);
    }
}