// @ts-nocheck
import {downLoadXlsx} from '@/utils/downloadfile';
import {request} from '@umijs/max';
import {paramsSortable} from "@/utils/utils";

/**
 * SERVICE: ${functionName}
 *
 * @date ${datetime}
 */
// 查询${functionName}列表
export const get${ClassName}List = (params?: ${ClassName}, sort: any) => {
    return request('/api/${moduleName}/${businessName}/list', {
        method: 'GET',
        params: paramsSortable(params, sort),
    }).then((res) => {
        return {
            data: res.data,
            total: res.total,
            success: true,
        };
    })
}

// 获取${functionName}详细信息
export const get${ClassName} = (id: string) => {
    return request(`/api/${moduleName}/${businessName}/${id}`, {
        method: 'GET',
    });
}

// 新增${functionName}
export const add${ClassName} = (params: ${ClassName}) => {
    return request('/api/${moduleName}/${businessName}', {
        method: 'POST',
        data: params,
    });
}

// 修改${functionName}
export const update${ClassName} = (params: ${ClassName}) => {
    return request(`/api/${moduleName}/${businessName}/${params.id}`, {
        method: 'PUT',
        data: params,
    });
}

// 删除${functionName}
export const remove${ClassName} = (ids: string) => {
    return request(`/api/${moduleName}/${businessName}/${ids}`, {
        method: 'DELETE',
    });
}

// 导出${functionName}列表
export const export${ClassName} = (params?: ${ClassName}) => {
    return downLoadXlsx(`/api/${moduleName}/${businessName}/export`, {params},
        `${moduleName}_${businessName}_$#{Number(new Date())}.xlsx`);
}
