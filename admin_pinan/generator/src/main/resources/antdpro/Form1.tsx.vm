// @ts-nocheck
import React, {useEffect, useRef, useState} from 'react';
import {
    ModalForm,
    ProFormRadio,
    ProFormSelect,
    ProFormText,
    ProFormTextArea,
    ProFormUploadButton,
    ProFormUploadDragger
} from '@ant-design/pro-components';
import {FormattedMessage, useIntl} from '@umijs/max';
import {message, Modal, Upload,} from 'antd';
import moment from 'moment';

export type ${ClassName}FormValueType = Record<string, unknown> & Partial<${ClassName}>;

export type ${ClassName}FormProps = {
    onCancel: (flag?: boolean, formVals?: ${ClassName}FormValueType) => void;
    onSubmit: (values: ${ClassName}FormValueType) => Promise<void>;
    visible: boolean;
    values: Partial<${ClassName}>;
};

const ${ClassName}Form: React.FC<${ClassName}FormProps> = (props) => {
    const [form] = Form.useForm();
    const id = Form.useWatch('id', form);
    // 国际化
    const intl = useIntl();
    // 编辑表单引用
    const formRef = useRef<ProFormInstance>();
    #if(${imageUpload} == true)
        const [previewOpen, setPreviewOpen] = useState(false);
        // 图片上传
        const [imageUrl, setImageUrl] = useState<string>();
        const [fileList, setFileList] = useState<any[]>([]);
    #end

    useEffect(() => {
        #if(${imageUpload} == true)
            setFileList(null)
            setImageUrl('')
            if (id) {
                // 编辑的时候设置fileList
                let fileOb = {
                    uid: moment().valueOf(),
                    status: 'done',
                    url: '/api' + props.values.imageUrl,
                }
                setFileList([fileOb])
                setImageUrl(props.values.imageUrl)
            }
        #end
        form.resetFields();
        form.setFieldsValue(props.values);
    }, [form, props]);

    // 提交表单
    const handleFinish = (values: Record<string, any>) => {
        #if(${imageUpload} == true)
            values.imageUrl = imageUrl;
        #end
        return props.onSubmit(values as ${ClassName}FormValueType);
    };

    // 关闭表单
    const handleClose = () => {
        props.onCancel();
    };

    return (
        <ModalForm
            title={id ? '编辑${functionName}' : '新增${functionName}'}
            open={props.visible}
            grid={true}
            form={form}
            onFinish={handleFinish}
            autoFocusFirstInput
            width={520}
            labelCol={{span: 4}}
            layout="horizontal"
            formRef={formRef}
            modalProps={{
                destroyOnClose: true,
                onCancel: handleClose,
            }}
        >
            #foreach ($column in $columns)
                #if(!$table.isSuperColumn($column.javaField))
                    #if($column.pk)
                        <ProFormText
                            name="${column.javaField}"
                            hidden={true}
                        />
                    #end
                    #if($column.insert && !$column.pk)
                        #if($column.htmlType == 'input')
                            <ProFormText
                                name="${column.javaField}"
                                label={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}',
                                    defaultMessage: '${column.columnComment}',
                                })}
                                placeholder={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}.placeholder',
                                    defaultMessage: '请填写${column.columnComment}',
                                })}
                                #if(${column.required} == true)
                                rules={[
                                    {
                                        required: ${column.required},
                                        message: <FormattedMessage
                                            id="${moduleName}.${businessName}.${column.javaField}.rule"
                                            defaultMessage="${column.columnComment}不能为空！"/>,
                                    },
                                ]}
                                #end
                            />
                        #elseif($column.htmlType == 'textarea')
                            <ProFormTextArea
                                name="${column.javaField}"
                                label={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}',
                                    defaultMessage: '${column.columnComment}',
                                })}
                                placeholder={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}.placeholder',
                                    defaultMessage: '请填写${column.columnComment}',
                                })}
                                #if(${column.required} == true)
                                rules={[
                                    {
                                        required: ${column.required},
                                        message: <FormattedMessage
                                            id="${moduleName}.${businessName}.${column.javaField}.rule"
                                            defaultMessage="${column.columnComment}不能为空！"/>,
                                    },
                                ]}
                                #end
                            />
                        #elseif($column.htmlType == 'select')
                            <ProFormSelect
                                name="${column.javaField}"
                                label={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}',
                                    defaultMessage: '${column.columnComment}',
                                })}
                                valueEnum={{
                                    open: '未解决',
                                    closed: '已解决',
                                }}
                                placeholder={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}.placeholder',
                                    defaultMessage: '请填写${column.columnComment}',
                                })}
                                #if(${column.required} == true)
                                rules={[
                                    {
                                        required: ${column.required},
                                        message: <FormattedMessage
                                            id="${moduleName}.${businessName}.${column.javaField}.rule"
                                            defaultMessage="请选择${column.columnComment}！"/>,
                                    },
                                ]}
                                #end
                            />
                        #elseif($column.htmlType == 'checkbox')
                            <ProFormCheckbox.Group
                                name="${column.javaField}"
                                label={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}',
                                    defaultMessage: '${column.columnComment}',
                                })}
                                options={['option1', 'option2', 'option3']}
                                placeholder={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}.placeholder',
                                    defaultMessage: '请填写${column.columnComment}',
                                })}
                                #if(${column.required} == true)
                                rules={[
                                    {
                                        required: ${column.required},
                                        message: <FormattedMessage
                                            id="${moduleName}.${businessName}.${column.javaField}.rule"
                                            defaultMessage="请选择${column.columnComment}！"/>,
                                    },
                                ]}
                                #end
                            />
                        #elseif($column.htmlType == 'radio')
                            <ProFormRadio.Group
                                name="${column.javaField}"
                                label={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}',
                                    defaultMessage: '${column.columnComment}',
                                })}
                                options={[
                                    {
                                        label: 'item 1',
                                        value: 'a',
                                    },
                                    {
                                        label: 'item 2',
                                        value: 'b',
                                    },
                                    {
                                        label: 'item 3',
                                        value: 'c',
                                    },
                                ]}
                                placeholder={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}.placeholder',
                                    defaultMessage: '请填写${column.columnComment}',
                                })}
                                #if(${column.required} == true)
                                rules={[
                                    {
                                        required: ${column.required},
                                        message: <FormattedMessage
                                            id="${moduleName}.${businessName}.${column.javaField}.rule"
                                            defaultMessage="请选择${column.columnComment}！"/>,
                                    },
                                ]}
                                #end
                            />
                        #elseif($column.htmlType == 'datetime')
                            <ProFormDatePicker
                                name="${column.javaField}"
                                label={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}',
                                    defaultMessage: '${column.columnComment}',
                                })}
                                placeholder="请选择${column.columnComment}"
                                #if(${column.required} == true)
                                rules={[
                                    {
                                        required: ${column.required},
                                        message: <FormattedMessage
                                            id="${moduleName}.${businessName}.${column.javaField}.rule"
                                            defaultMessage="请选择${column.columnComment}！"/>,
                                    },
                                ]}
                                #end
                            />
                        #elseif($column.htmlType == 'imageUpload')
                            <ProFormUploadButton
                                name="${column.javaField}"
                                label={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}',
                                    defaultMessage: '${column.columnComment}',
                                })}
                                valuePropName="file"
                                fieldProps={{
                                    action: `/api/common/upload`,
                                    headers: {
                                        Authorization: 'Bearer ' + localStorage.getItem('access_token'),
                                    },
                                    name: 'file',
                                    maxCount: 1,
                                    listType: 'picture-card',
                                    accept: 'image/*',
                                    onRemove: (e) => {
                                        // 点击删除的时候直接清空fileList
                                        setFileList(null)
                                    },
                                    beforeUpload: (file) => {
                                        if (fileList?.length > 0) {
                                            message.error('只能上传一张图片')
                                            return Upload.LIST_IGNORE
                                        }
                                        const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
                                        if (!isJpgOrPng) {
                                            message.error('只允许上传 JPG/PNG 格式的文件');
                                        }
                                        const isLt3M = file.size / 1024 / 1024 < 3;
                                        if (!isLt3M) {
                                            message.error('仅支持3M以下的文件');
                                        }
                                        if ((isJpgOrPng && isLt3M) === false) {
                                            setFileList(null)
                                            setImageUrl(undefined);
                                            formRef.current?.resetFields(['resetFields'])
                                            return Upload.LIST_IGNORE
                                        } else {
                                            return true
                                        }
                                    },
                                    onChange: (info) => {
                                        if (info.file.status == 'removed') {
                                            setFileList(null)
                                        }
                                        if (info.file.status === 'done') {
                                            // 做成fileList的格式传回
                                            if (info.file.response.code !== 200) {
                                                message.error(info.file.response.msg)
                                                return;
                                            }
                                            let obj = {
                                                uid: moment().valueOf(),
                                                name: info.file.response.data.originalFilename,
                                                status: 'done',
                                                url: info.file.response.data.url
                                            }
                                            setFileList([obj])
                                            setImageUrl(info.file.response.data.fileName);
                                            return;
                                        }
                                        if (info.file.status == 'error') {
                                            message.error('上传失败!请稍后再试')
                                            setFileList([])
                                        }
                                    },
                                    onPreview: (file: UploadFile) => {
                                        setPreviewOpen(true)
                                    },
                                }}
                                fileList={fileList}
                                extra={'需加盖公章'}
                                placeholder={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}.placeholder',
                                    defaultMessage: '请上传${column.columnComment}',
                                })}
                                #if(${column.required} == true)
                                rules={[
                                    {
                                        required: ${column.required},
                                        message: <FormattedMessage
                                            id="${moduleName}.${businessName}.${column.javaField}.rule"
                                            defaultMessage="请上传${column.columnComment}！"/>,
                                    },
                                ]}
                                #end
                            />
                            {/* 页面不显示这个组件 点击预览的时候模拟点击这个组件 效果是一样的 */}
                            <Modal open={previewOpen} footer={null} onCancel={() => setPreviewOpen(false)}>
                                <img alt="example" style={{width: '100%'}} src={"/api" + imageUrl}/>
                            </Modal>
                        #elseif($column.htmlType == 'fileUpload')
                            <ProFormUploadDragger
                                name="${column.javaField}"
                                label={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}',
                                    defaultMessage: '${column.columnComment}',
                                })}
                                valuePropName="file"
                                placeholder={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}.placeholder',
                                    defaultMessage: '请上传${column.columnComment}',
                                })}
                                extra={
                                    <>
                                        下载{' '}
                                        <a
                                            download="文件模板.xlsx"
                                            href={'https://x.4001.cn/import-template.xlsx'}
                                            rel="noreferrer"
                                        >
                                            文件模板
                                        </a>
                                    </>
                                }
                                fieldProps={{
                                    accept: '.xls, .xlsx',
                                    action: `/api/common/upload`,
                                    headers: {
                                        Authorization: 'Bearer ' + localStorage.getItem('access_token'),
                                    },
                                    name: 'file',
                                    maxCount: 1,
                                    // defaultFileList: uploadedFileList,
                                    // onChange: handleUpload,
                                }}
                                #if(${column.required} == true)
                                rules={[
                                    {
                                        required: ${column.required},
                                        message: <FormattedMessage
                                            id="${moduleName}.${businessName}.${column.javaField}.rule"
                                            defaultMessage="请上传${column.columnComment}！"/>,
                                    },
                                ]}
                                #end
                            />
                        #else
                            <ProFormText
                                name="${column.javaField}"
                                label={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}',
                                    defaultMessage: '${column.columnComment}',
                                })}
                                placeholder={intl.formatMessage({
                                    id: '${moduleName}.${businessName}.${column.javaField}.placeholder',
                                    defaultMessage: '请上传${column.columnComment}',
                                })}
                                #if(${column.required} == true)
                                rules={[
                                    {
                                        required: ${column.required},
                                        message: <FormattedMessage
                                            id="${moduleName}.${businessName}.${column.javaField}.rule"
                                            defaultMessage="${column.columnComment}不能为空！"/>,
                                    },
                                ]}
                                #end
                            />
                        #end
                    #end
                #end
            #end
        </ModalForm>
    );
};

export default ${ClassName}Form;
