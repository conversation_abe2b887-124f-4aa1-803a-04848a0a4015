// @ts-nocheck
import {DeleteOutlined, DownloadOutlined, ExclamationCircleOutlined, PlusOutlined} from '@ant-design/icons';
import {Button, message, Modal,} from 'antd';
import React, {useRef, useState} from 'react';
import {FormattedMessage, useAccess, useIntl} from '@umijs/max';

import {ActionType, FooterToolbar, FormInstance, PageContainer, ProColumns, ProTable} from '@ant-design/pro-components';
import {add${ClassName}, export${ClassName}, remove${ClassName}, update${ClassName}} from '@/services/';
import ${ClassName}Form from './edit';

const ${ClassName}TableList: React.FC = () => {
    const [messageApi, contextHolder] = message.useMessage();
    const [modal, ctx] = Modal.useModal();
    const [searchData, setSearchData] = useState({});
    // ProTable 组件操作引用
    const actionRef = useRef<ActionType>();
    // ProTable 查询表单引用
    const formTableRef = useRef<FormInstance>();
    // 选中状态
    const [currentRow, setCurrentRow] = useState<${ClassName}>();
    const [selectedRows, setSelectedRows] = useState<${ClassName}[]>([]);
    // 显示状态
    const [modalVisible, setModalVisible] = useState<boolean>(false);
    // 页面权限
    const access = useAccess();
    // 国际化
    const intl = useIntl();

    const columns: ProColumns<${ClassName}>[] = [
        #foreach ($column in $columns)
            #set($sorter="false")
            #if($column.javaType == "Integer")
                #set($valueType="digit")
                #set($sorter="true")
                #set($width="80")
                #set($ellipsis="false")
            #elseif($column.javaType == "Long")
                #set($valueType="text")
                #set($sorter="true")
                #set($width="120")
                #set($ellipsis="false")
            #elseif($column.javaType == "BigInteger")
                #set($valueType="digit")
                #set($sorter="true")
                #set($width="120")
                #set($ellipsis="false")
            #elseif($column.javaType == "BigDecimal")
                #set($valueType="digit")
                #set($sorter="true")
                #set($width="120")
                #set($ellipsis="false")
            #elseif($column.javaType == "Float")
                #set($valueType="digit")
                #set($sorter="true")
                #set($width="120")
                #set($ellipsis="false")
            #elseif($column.javaType == "Decimal")
                #set($valueType="digit")
                #set($sorter="true")
                #set($width="120")
                #set($ellipsis="false")
            #elseif($column.javaType == "Boolean")
                #set($valueType="select")
                #set($sorter="true")
                #set($width="80")
                #set($ellipsis="false")
            #elseif($column.javaType == "Date")
                #set($valueType="dateTime")
                #set($sorter="true")
                #set($width="120")
                #set($ellipsis="false")
                #if($column.queryType == "BETWEEN")
                    #set($valueType="dateRange")
                #end
            #else
                #set($valueType="text")
                #set($ellipsis="true")
                #set($width="120")
            #end
            #set($hideInSearch = !$column.query)
            #set($hideInTable = $table.isSuperColumn($column.javaField) || !$column.list)
            #if(!$table.isSuperColumn($column.javaField) && !($hideInSearch == true && $hideInTable == true))
                {
                    title: <FormattedMessage id="${moduleName}.${businessName}.${column.javaField}"
                                             defaultMessage="${column.columnComment}"/>,
                    dataIndex: '$column.javaField',
                    #if($hideInSearch == true)
                        hideInSearch: true,
                    #end
                    #if($hideInTable == true)
                        hideInTable: true,
                    #end
                    valueType: '$valueType',
                    #if($sorter == true)
                        sorter: true,
                        defaultSortOrder: 'descend',
                    #end
                    width: $width,
                    #if($ellipsis == true)
                        ellipsis: $ellipsis,
                    #end
                    ##              #if($valueType == 'digit')
                    ##                  align: 'right',
                    ##              #else
                    align: 'center',
                    ##              #end
                    #if($valueType == 'dateRange')
                        render: (_, record) => <span>{record.createTime}</span>,
                        search: {
                            transform: (value) => {
                                return {
                                    'params[beginTime]': value[0],
                                    'params[endTime]': value[1],
                                };
                            },
                        },
                    #end
                },
            #end
        #end
        {
            title: <FormattedMessage id="biz.common.column.createTime" defaultMessage="创建时间"/>,
            dataIndex: 'createTime',
            hideInSearch: true,
            sorter: true,
            width: 120,
            defaultSortOrder: 'descend',
            fixed: 'right',
            align: 'center',
            render: (_, record) => <span>{record.createTime}</span>,
            search: {
                transform: (value) => {
                    return {
                        'params[beginTime]': value[0],
                        'params[endTime]': value[1],
                    };
                },
            },
        },
        {
            title: <FormattedMessage id="pages.searchTable.titleOption" defaultMessage="操作"/>,
            dataIndex: 'option',
            width: 120,
            align: 'center',
            fixed: 'right',
            valueType: 'option',
            render: (_, record) => [
                <Button
                    type="link"
                    size="small"
                    key="edit"
                    hidden={!access.hasPerms('${moduleName}:${businessName}:edit')}
                    onClick={() => {
                        setCurrentRow(record);
                        setModalVisible(true);
                    }}
                >
                    <FormattedMessage id="pages.searchTable.edit" defaultMessage="编辑"/>
                </Button>,
                <Button
                    type="link"
                    size="small"
                    danger
                    key="remove"
                    hidden={!access.hasPerms('${moduleName}:${businessName}:remove')}
                    onClick={async () => {
                        modal.confirm({
                            title: intl.formatMessage({
                                id: 'pages.searchTable.delete.confirm',
                                defaultMessage: '确定删除已选中项吗？',
                            }),
                            icon: <ExclamationCircleOutlined/>,
                            okText: intl.formatMessage({
                                id: 'pages.searchTable.delete.confirm.ok',
                                defaultMessage: '确认',
                            }),
                            cancelText: intl.formatMessage({
                                id: 'pages.searchTable.delete.confirm.cancel',
                                defaultMessage: '取消',
                            }),
                            onOk: async () => {
                                if (!selectedRows) return;
                                try {
                                    await remove${ClassName}(String(record.${pkColumn.javaField}));
                                    message.success('删除成功');
                                    setSelectedRows([]);
                                    actionRef.current?.reloadAndRest?.();
                                } catch (error) {
                                    console.log(error);
                                }
                            },
                            onCancel() {
                            },
                        });
                    }}
                >
                    <FormattedMessage id="pages.searchTable.delete" defaultMessage="删除"/>
                </Button>
            ],
        },
    ];

    return (
        <PageContainer>
            {contextHolder}
            {ctx}
            <ProTable<${ClassName}>
                ## headerTitle={intl.formatMessage({
                ##     id: '${moduleName}.${businessName}.tableName',
                ##     defaultMessage: '${functionName}列表',
                ## })}
                ##  pagination={{defaultPageSize: 10, showSizeChanger: true,}}
                actionRef={actionRef}
                formRef={formTableRef}
                rowKey="${pkColumn.javaField}"
                key="${className}List"
                scroll={{x: 'max-content'}}
                sticky={{offsetHeader: 54, offsetScroll: 5}}
                ## search={{
                ##     labelWidth: 'auto',
                ## }}
                toolBarRender={() => [
                    <Button
                        type="primary"
                        key="add"
                        hidden={!access.hasPerms('${moduleName}:${businessName}:add')}
                        onClick={async () => {
                            setCurrentRow(undefined);
                            setModalVisible(true);
                        }}
                    >
                        <PlusOutlined/>
                        <FormattedMessage id="pages.searchTable.new" defaultMessage="新建"/>
                    </Button>,
                    <Button
                        type="primary"
                        key="remove"
                        danger
                        hidden={selectedRows?.length === 0 || !access.hasPerms('${moduleName}:${businessName}:remove')}
                        onClick={async () => {
                            modal.confirm({
                                title: intl.formatMessage({
                                    id: 'pages.searchTable.delete.confirm',
                                    defaultMessage: '确定删除已选中项吗？',
                                }),
                                icon: <ExclamationCircleOutlined/>,
                                okText: intl.formatMessage({
                                    id: 'pages.searchTable.delete.confirm.ok',
                                    defaultMessage: '确认',
                                }),
                                cancelText: intl.formatMessage({
                                    id: 'pages.searchTable.delete.confirm.cancel',
                                    defaultMessage: '取消',
                                }),
                                onOk: async () => {
                                    if (!selectedRows) return;
                                    try {
                                        await remove${ClassName}(selectedRows.map((row) => row.${pkColumn.javaField}).join(','));
                                        message.success('删除成功');
                                        setSelectedRows([]);
                                        actionRef.current?.reloadAndRest?.();
                                    } catch (error) {
                                        console.log(error);
                                    }
                                },
                                onCancel() {
                                },
                            });
                        }}
                    >
                        <DeleteOutlined/>
                        <FormattedMessage id="pages.searchTable.delete" defaultMessage="删除"/>
                    </Button>,
                    <Button
                        type="primary"
                        key="export"
                        hidden={!access.hasPerms('${moduleName}:${businessName}:export')}
                        onClick={async () => {
                            const hide = message.loading('正在导出');
                            try {
                                await export${ClassName}(formTableRef.current?.getFieldsFormatValue?.());
                                hide();
                                message.success('导出成功');
                            } catch (error) {
                                hide();
                            }
                        }}
                    >
                        <DownloadOutlined/>{' '}
                        <FormattedMessage id="pages.searchTable.export" defaultMessage="导出"/>
                    </Button>,
                ]}
                onReset={() => {
                    setSearchData({});
                }}
                request={(params, sort) =>
                    get${ClassName}List(Object.assign(params, searchData), sort)}
                columns={columns}
                rowSelection={{
                    onChange: (_, rows) => {
                        setSelectedRows(rows);
                    },
                }}
            />
            {selectedRows?.length > 0 && (
                <FooterToolbar
                    extra={
                        <div>
                            <FormattedMessage id="pages.searchTable.chosen" defaultMessage="已选择"/>
                            <a style={{fontWeight: 600}}>{selectedRows.length}</a>
                            <FormattedMessage id="pages.searchTable.item" defaultMessage="项"/>
                        </div>
                    }
                >
                    <Button
                        key="batchRemove"
                        hidden={!access.hasPerms('${moduleName}:${businessName}:remove')}
                        onClick={async () => {
                            modal.confirm({
                                title: intl.formatMessage({
                                    id: 'pages.searchTable.delete.confirm',
                                    defaultMessage: '确定删除已选中项吗？',
                                }),
                                okText: intl.formatMessage({
                                    id: 'pages.searchTable.delete.confirm.ok',
                                    defaultMessage: '确认',
                                }),
                                cancelText: intl.formatMessage({
                                    id: 'pages.searchTable.delete.confirm.cancel',
                                    defaultMessage: '取消',
                                }),
                                onOk: async () => {
                                    if (!selectedRows) return;
                                    try {
                                        await remove${ClassName}(selectedRows.map((row) => row.${pkColumn.javaField}).join(','));
                                        message.success('删除成功');
                                        setSelectedRows([]);
                                        actionRef.current?.reloadAndRest?.();
                                    } catch (error) {
                                        console.log(error);
                                    }
                                },
                            });
                        }}
                    >
                        <FormattedMessage id="pages.searchTable.batchDeletion" defaultMessage="批量删除"/>
                    </Button>
                </FooterToolbar>
            )}
            <${ClassName}Form
                onSubmit={async (values) => {
                    return new Promise(async (resolve, reject) => {
                        let success = false;
                        try {
                            if (values.${pkColumn.javaField}) {
                                await update${ClassName}({...values} as ${ClassName});
                            } else {
                                await add${ClassName}({...values} as ${ClassName});
                            }
                            success = true;
                        } catch (error) {
                            success = false;
                            console.log(error)
                        }
                        if (success) {
                            resolve();
                            setModalVisible(false);
                            setCurrentRow(undefined);
                            if (actionRef.current) {
                                actionRef.current.reload();
                            }
                        } else {
                            reject();
                        }
                    });
                }}
                onCancel={() => {
                    setModalVisible(false);
                    setCurrentRow(undefined);
                }}
                visible={modalVisible}
                values={currentRow || {}}
            />
        </PageContainer>
    );
};

export default ${ClassName}TableList;
