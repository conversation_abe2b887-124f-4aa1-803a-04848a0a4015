package ${packageName}.vo.putvo;

    #foreach ($import in $importList)
    import ${import};
    #end
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Size;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;
#if($table.crud || $table.sub)
#elseif($table.tree)
#end

/**
 * ${functionName} 请求 报文
 *
 * <AUTHOR>
 * @date ${datetime}
 */
    #if($table.crud || $table.sub)
        #set($Entity="BaseEntity")
    #elseif($table.tree)
        #set($Entity="TreeEntity")
    #end
        @ApiModel(value = "${functionName} 修改参数")
        @Data
        public class ${ClassName}PutVo {

    #foreach ($column in $columns)
        #if($column.javaField != 'id' && $column.javaField != 'createBy'
        && $column.javaField != 'createTime' && $column.javaField != 'updateBy' && $column.javaField != 'updateTime')
            #if($column.list)
                #set($parentheseIndex=$column.columnComment.indexOf("（"))
                #if($parentheseIndex != -1)
                    #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                #else
                    #set($comment=$column.columnComment)
                #end
            #end
            #if($column.javaField != $pkColumn.javaField)
                #if($column.required && $column.javaType == "String")
                @NotBlank(message = "$comment 不能为空")
                #end
                #if($column.required && $column.javaType != "String")
                @NotNull(message = "$comment 不能为空")
                #end
                #if($column.columnLength && $column.columnLength > 0)
                @Size(max = $column.columnLength, message = "$column.columnComment 不能超过 $column.columnLength 个字符")
                @ApiModelProperty(name = "$column.javaField", value = "$column.columnComment", allowableValues = "最大长度$column.columnLength")
                #else
                @ApiModelProperty(name = "$column.javaField", value = "$column.columnComment")
                #end
            private $column.javaType $column.javaField;

            #end
        #end
    #end
    #if($table.sub)

        @ApiModelProperty("$table.subTable.functionName信息")
        private List<${subClassName}> ${subclassName}List;

    #end
}
