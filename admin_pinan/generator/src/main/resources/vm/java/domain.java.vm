package ${packageName}.domain;

    #foreach ($import in $importList)
    import ${import};
    #end
import lombok.Data;
import lombok.EqualsAndHashCode;
#if($table.crud || $table.sub)
#elseif($table.tree)
#end

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
    #if($table.crud || $table.sub)
        #set($Entity="BaseEntity")
    #elseif($table.tree)
        #set($Entity="TreeEntity")
    #end
        @ApiModel(value = "${functionName} 对象")
        @Data
        @EqualsAndHashCode(callSuper = true)
        public class ${ClassName} extends ${Entity}{
        private static final long serialVersionUID = 1L;
    #foreach ($column in $columns)
        #if(!$table.isSuperColumn($column.javaField))
            #if($column.columnComment) /** $column.columnComment*/ #end
            #if($column.list)
                #set($parentheseIndex=$column.columnComment.indexOf("（"))
                #if($parentheseIndex != -1)
                    #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                #else
                    #set($comment=$column.columnComment)
                #end
                #if($column.javaType == 'Date')
                @JsonFormat(pattern = "yyyy-MM-dd")
                #end
            #end
        @ApiModelProperty(name = "$column.javaField", value = "$column.columnComment")
        private $column.javaType $column.javaField;

        #end
    #end
    #if($table.sub)
            /** $table.subTable.functionName信息 */
            private List<${subClassName}> ${subclassName}List;
    #end
        }
