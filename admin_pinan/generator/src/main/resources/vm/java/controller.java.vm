package ${packageName}.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ali.common.core.domain.AjaxResult;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.ali.common.annotation.Log;
import com.ali.common.core.controller.BaseController;
import com.ali.common.enums.BusinessType;

import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ali.common.utils.poi.ExcelUtil;
#if($table.crud || $table.sub)
import com.ali.common.core.page.AjaxResult;
#elseif($table.tree)
#end

/**
 * ${functionName}Controller
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Api(tags = "${functionName}相关接口")
@Validated
@RestController
public class ${ClassName}Controller extends BaseController {
    @Autowired
    private I${ClassName}Service ${className}Service;

@ApiOperation("查询${functionName}列表")
@PreAuthorize("@ss.hasPermi('${permissionPrefix}:list')")
@GetMapping("/${moduleName}/${businessName}/list")
    #if($table.crud || $table.sub)
    public AjaxResult<${ClassName}> list(${ClassName} ${className}) {
        startPage();
        return AjaxResult.getDataTable(${className}Service.select${ClassName}List(${className}));
    }
    #elseif($table.tree)
        public AjaxResult<List<${ClassName}>> list(${ClassName} ${className}) {
            return AjaxResult.success(${className}Service.select${ClassName}List(${className}));
        }
    #end

    @ApiOperation("导出${functionName}列表")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:export')")
    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
    @PostMapping("/${moduleName}/${businessName}/export")
    public void export(HttpServletResponse response, ${ClassName} ${className}) {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        ExcelUtil<${ClassName}> util = new ExcelUtil<>(${ClassName}. class);
        util.exportExcel(response, list, "${functionName}数据");
    }

    @ApiOperation("获取${functionName}详细信息")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:query')")
    @GetMapping(value = "/${moduleName}/${businessName}/{${pkColumn.javaField}}")
    public AjaxResult<${ClassName}> getInfo(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField}) {
        return AjaxResult.success(${className}Service.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField}));
    }

    @ApiOperation("新增${functionName}")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:add')")
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @PostMapping("/${moduleName}/${businessName}")
    public AjaxResult<String> add(@Valid @RequestBody ${ClassName}PostVo postVo) {
        return toAjax(${className}Service.insert${ClassName}(${ClassName}Convert.postVoTo${ClassName}(postVo)));
    }

    @ApiOperation("修改${functionName}")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:edit')")
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @PutMapping("/${moduleName}/${businessName}/{${pkColumn.javaField}}")
    public AjaxResult<String> edit(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField},
                                   @Valid @RequestBody ${ClassName}PutVo putVo) {
        return toAjax(${className}Service.update${ClassName}(${ClassName}Convert.putVoTo${ClassName}(${pkColumn.javaField}, putVo)));
    }

    @ApiOperation("删除${functionName}")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:remove')")
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
    @DeleteMapping("/${moduleName}/${businessName}/{${pkColumn.javaField}s}")
    public AjaxResult<String> remove(@PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        return toAjax(${className}Service.delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaField}s));
    }
}
