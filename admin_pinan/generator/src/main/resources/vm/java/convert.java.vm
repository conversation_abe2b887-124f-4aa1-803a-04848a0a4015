package ${packageName}.convert;

    #foreach ($import in $importList)
    import ${import};
    #end
import org.springframework.util.ObjectUtils;
import lombok.Data;

#if($table.crud || $table.sub)
#elseif($table.tree)
#end

/**
 * ${functionName} 转换类
 *
 * <AUTHOR>
 * @date ${datetime}
 */
    #if($table.crud || $table.sub)
        #set($Entity="BaseEntity")
    #elseif($table.tree)
        #set($Entity="TreeEntity")
    #end
        public class ${ClassName}Convert {
    /**
     * ${ClassName}PostVo 转换为 ${ClassName}
     */
    public static ${ClassName} postVoTo${ClassName}(${ClassName}PostVo postVo) {
        ${ClassName} ${className} =new ${ClassName}();
        if (ObjectUtils.isEmpty(postVo)) {
            return ${className};
        }
        #foreach ($column in $columns)
            #if($column.javaField != $pkColumn.javaField && $column.javaField != 'createBy' && $column.javaField !=
                'createTime'
            && $column.javaField != 'updateBy' && $column.javaField != 'updateTime')
                #set($AttrName=$column.fnJavaField)
                ${className}.set${AttrName}(postVo.get${AttrName}());
            #end
        #end
        return ${className};
    }

    /**
     *  ${ClassName}PutVo 转换为 ${ClassName}
     */
    public static ${ClassName} putVoTo${ClassName}(${pkColumn.javaType} ${pkColumn.javaField}, ${ClassName}PutVo putVo) {
        ${ClassName} ${className} =new ${ClassName}();
        if (ObjectUtils.isEmpty(putVo)) {
            return ${className};
        }
        #foreach ($column in $columns)
            #set($AttrName=$column.fnJavaField)
            #if($column.javaField != $pkColumn.javaField && $column.javaField != 'createBy'
            && $column.javaField != 'createTime' && $column.javaField != 'updateBy' && $column.javaField !=
                'updateTime')
                ${className}.set${AttrName}(putVo.get${AttrName}());
            #end
            #if($column.javaField==$pkColumn.javaField)
                ${className}.set${AttrName}($pkColumn.javaField);
            #end
        #end
        return ${className};
    }

}
