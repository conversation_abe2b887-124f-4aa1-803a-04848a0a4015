package com.ali.web.controller.system;

import com.ali.common.annotation.Anonymous;
import com.ali.common.core.domain.AjaxResult;
import com.ali.system.service.ISysConfigService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 首页
 *
 * <AUTHOR>
 */
@RestController
public class SysIndexController {
    @Resource
    private ISysConfigService configService;

    /**
     * 访问首页，提示语
     */
    @RequestMapping("/")
    public String index() {
        return "非法访问！";
    }

    @Anonymous
    @RequestMapping("/nosign/config")
    public AjaxResult<Map<String, String>> list() {
        Map<String, String> data = new HashMap<>();
        data.put("sys.copyright", configService.selectConfigByKey("sys.copyright"));
        data.put("sys.name", configService.selectConfigByKey("sys.name"));
        data.put("sys.subtitle", configService.selectConfigByKey("sys.subtitle"));
        data.put("sys.app.logo", configService.selectConfigByKey("sys.app.logo"));
        data.put("sys.app.tabIco", configService.selectConfigByKey("sys.app.tabIco"));
        return AjaxResult.success(data);
    }

}
