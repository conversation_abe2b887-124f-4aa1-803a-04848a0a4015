package com.ali.web.controller.system;

import com.ali.common.annotation.Log;
import com.ali.common.config.GlobalConfig;
import com.ali.common.core.controller.BaseController;
import com.ali.common.core.domain.AjaxResult;
import com.ali.common.core.domain.entity.SysUser;
import com.ali.common.core.domain.model.LoginUser;
import com.ali.common.enums.BusinessType;
import com.ali.common.utils.GoogleAuthenticator;
import com.ali.common.utils.SecurityUtils;
import com.ali.common.utils.StringUtils;
import com.ali.common.utils.file.FileUploadUtils;
import com.ali.common.utils.file.MimeTypeUtils;
import com.ali.framework.web.service.TokenService;
import com.ali.system.service.ISysConfigService;
import com.ali.system.service.ISysUserService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 个人信息 业务处理
 *
 * <AUTHOR>
 */
@Api(tags = "个人信息相关接口")
@RestController
@RequestMapping("/system/user/profile")
public class SysProfileController extends BaseController {
    @Autowired
    private ISysUserService userService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysConfigService configService;

    /**
     * 个人信息
     */
    @GetMapping
    public AjaxResult profile() {
        LoginUser loginUser = getLoginUser();
        SysUser user = loginUser.getUser();
        Map<String, Object> map = new HashMap<>();
        user.setPassword(null);
        if (ObjectUtils.isNotEmpty(user.getGoogleToken()))
            user.setGoogleToken("******");
        map.put("user", user);
        map.put("roleGroup", userService.selectUserRoleGroup(loginUser.getUsername()));
        map.put("postGroup", userService.selectUserPostGroup(loginUser.getUsername()));
        return AjaxResult.success(map);
    }

    /**
     * 修改用户
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult updateProfile(@RequestBody SysUser user) {
        LoginUser loginUser = getLoginUser();
        SysUser currentUser = loginUser.getUser();
        currentUser.setNickName(user.getNickName());
        currentUser.setEmail(user.getEmail());
        currentUser.setPhonenumber(user.getPhonenumber());
        currentUser.setSex(user.getSex());
        if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(currentUser)) {
            return error("修改用户'" + loginUser.getUsername() + "'失败，手机号码已存在");
        }
        if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(currentUser)) {
            return error("修改用户'" + loginUser.getUsername() + "'失败，邮箱账号已存在");
        }
        if (userService.updateUserProfile(currentUser) > 0) {
            // 更新缓存用户信息
            tokenService.setLoginUser(loginUser);
            return success();
        }
        return error("修改个人信息异常，请联系管理员");
    }

    /**
     * 重置密码
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    public AjaxResult updatePwd(String oldPassword, String newPassword) {
        LoginUser loginUser = getLoginUser();
        String userName = loginUser.getUsername();
        String password = loginUser.getPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, password)) {
            return error("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, password)) {
            return error("新密码不能与旧密码相同");
        }
        newPassword = SecurityUtils.encryptPassword(newPassword);
        if (userService.resetUserPwd(userName, newPassword) > 0) {
            // 更新缓存用户密码
            loginUser.getUser().setPassword(newPassword);
            tokenService.setLoginUser(loginUser);
            return success();
        }
        return error("修改密码异常，请联系管理员");
    }

    /**
     * 头像上传
     */
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping("/avatar")
    public AjaxResult avatar(@RequestParam("avatarfile") MultipartFile file) throws Exception {
        if (!file.isEmpty()) {
            LoginUser loginUser = getLoginUser();
            String avatar = FileUploadUtils.upload(GlobalConfig.getAvatarPath(), file, MimeTypeUtils.IMAGE_EXTENSION);
            if (userService.updateUserAvatar(loginUser.getUsername(), avatar)) {
                Map ajax = new HashMap();
                ajax.put("imgUrl", avatar);
                // 更新缓存用户头像
                loginUser.getUser().setAvatar(avatar);
                tokenService.setLoginUser(loginUser);
                return AjaxResult.success(ajax);
            }
        }
        return error("上传图片异常，请联系管理员");
    }

    /**
     * 获取谷歌绑定密钥
     */
    @ApiOperation("获取谷歌绑定密钥")
    @GetMapping("/getGoogleCode")
    public AjaxResult getGoogleCode() {
        if (null == SecurityUtils.getUserId())
            return error("用户信息为空");
        String secretKey = GoogleAuthenticator.generateSecretKey();
        Map<String, String> param = new HashMap<>();
        param.put("userName", SecurityUtils.getUsername());
        param.put("url", GoogleAuthenticator.getQRBarcode(SecurityUtils.getUsername() + "@" +
                configService.selectConfigByKey("sys.name"), secretKey));
        param.put("googleAuthSecretKey", secretKey);
        return AjaxResult.success(param);
    }

    @PostMapping({"/googleBind"})
    public AjaxResult cxBindGoogleAuth(@RequestBody String paramJson) {
        if (null == SecurityUtils.getUserId()) {
            return error("用户信息为空");
        }
        JSONObject param = JSON.parseObject(paramJson);
        String googleToken = param.getString("googleToken");
        String code1 = param.getString("code");
        if (StringUtils.isEmpty(code1) || StringUtils.isEmpty(googleToken)) {
            return error("请输入正确谷歌验证码");
        }
        Long code = Long.parseLong(code1);
        // 校验 code 是否正确
        if (!GoogleAuthenticator.checkByThrowException(code, googleToken)) {
            return error("谷歌验证码错误");
        }
        SysUser user = new SysUser();
        user.setUserId(SecurityUtils.getUserId());
        user.setGoogleToken(googleToken);
        int rows = userService.updateUserProfile(user);
        if (rows > 0) {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            loginUser.getUser().setGoogleToken(googleToken);
            tokenService.refreshToken(loginUser);
        }
        return toAjax(rows);
    }
}
