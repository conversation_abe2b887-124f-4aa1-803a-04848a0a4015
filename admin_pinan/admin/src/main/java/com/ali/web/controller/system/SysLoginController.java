package com.ali.web.controller.system;

import com.ali.biz.domain.LoanUser;
import com.ali.biz.service.ILoanUserService;
import com.ali.common.core.domain.AjaxResult;
import com.ali.common.core.domain.entity.SysMenu;
import com.ali.common.core.domain.entity.SysUser;
import com.ali.common.core.domain.model.LoginBody;
import com.ali.common.utils.SecurityUtils;
import com.ali.framework.web.service.SysLoginService;
import com.ali.framework.web.service.SysPermissionService;
import com.ali.system.service.ISysMenuService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController {
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private ILoanUserService loanUserService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/h5Login")
    public AjaxResult<String> h5Login(@RequestBody LoginBody loginBody) {
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid(), "h5");
        return AjaxResult.success(token);
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/h5GetInfo")
    public AjaxResult<LoanUser> h5GetInfo() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        return AjaxResult.success(loanUserService.selectLoanUserById(user.getUserId()));
    }


    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult<String> login(@RequestBody LoginBody loginBody) {
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid(), "admin");
        return AjaxResult.success(token);
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult<Map<String, Object>> getInfo() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if (ObjectUtils.isNotEmpty(user.getGoogleToken())) {
            user.setGoogleToken("******");
        }
        user.setPassword(null);
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        Map<String, Object> ajax = new HashMap<>();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return AjaxResult.success(ajax);
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters() {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
