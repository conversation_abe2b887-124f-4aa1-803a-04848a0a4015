package com.ali.biz.service.impl;

import com.ali.biz.domain.LoanUser;
import com.ali.biz.mapper.LoanUserMapper;
import com.ali.biz.service.ILoanUserService;
import com.ali.common.utils.SecurityUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 贷款用户Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Service
public class LoanUserServiceImpl implements ILoanUserService {
    @Autowired
    private LoanUserMapper loanUserMapper;

    @Override
    public LoanUser selectLoanUserByPhone(String phone) {
        return loanUserMapper.selectLoanUserByPhone(phone);
    }

    /**
     * 查询贷款用户
     *
     * @param id 贷款用户主键
     * @return 贷款用户
     */
    @Override
    public LoanUser selectLoanUserById(Long id) {
        return loanUserMapper.selectLoanUserById(id);
    }

    /**
     * 查询贷款用户列表
     *
     * @param loanUser 贷款用户
     * @return 贷款用户
     */
    @Override
    public List<LoanUser> selectLoanUserList(LoanUser loanUser) {
        return loanUserMapper.selectLoanUserList(loanUser);
    }

    /**
     * 新增贷款用户
     *
     * @param loanUser 贷款用户
     * @return 结果
     */
    @Override
    public int insertLoanUser(LoanUser loanUser) {
        loanUser.setPassword(SecurityUtils.encryptPassword(loanUser.getPhoneNumber()));
        int res = loanUserMapper.insertLoanUser(loanUser);
        Long sort = loanUser.getSort();
        if (ObjectUtils.isEmpty(sort)) {
            loanUser.setSort(loanUser.getId());
            loanUserMapper.updateLoanUser(loanUser);
        }
        return res;
    }

    /**
     * 修改贷款用户
     *
     * @param loanUser 贷款用户
     * @return 结果
     */
    @Override
    public int updateLoanUser(LoanUser loanUser) {
        return loanUserMapper.updateLoanUser(loanUser);
    }

    /**
     * 批量删除贷款用户
     *
     * @param ids 需要删除的贷款用户主键
     * @return 结果
     */
    @Override
    public int deleteLoanUserByIds(Long[] ids) {
        return loanUserMapper.deleteLoanUserByIds(ids);
    }

    /**
     * 删除贷款用户信息
     *
     * @param id 贷款用户主键
     * @return 结果
     */
    @Override
    public int deleteLoanUserById(Long id) {
        return loanUserMapper.deleteLoanUserById(id);
    }
}
