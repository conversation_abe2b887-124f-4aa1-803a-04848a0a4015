package com.ali.biz.service.impl;

import com.ali.biz.domain.LoanInfo;
import com.ali.biz.mapper.LoanInfoMapper;
import com.ali.biz.service.ILoanInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 贷款信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Service
public class LoanInfoServiceImpl implements ILoanInfoService {
    @Autowired
    private LoanInfoMapper loanInfoMapper;

    /**
     * 查询贷款信息
     *
     * @param id 贷款信息主键
     * @return 贷款信息
     */
    @Override
    public LoanInfo selectLoanInfoById(Long id) {
        return loanInfoMapper.selectLoanInfoById(id);
    }

    /**
     * 查询贷款信息列表
     *
     * @param loanInfo 贷款信息
     * @return 贷款信息
     */
    @Override
    public List<LoanInfo> selectLoanInfoList(LoanInfo loanInfo) {
        return loanInfoMapper.selectLoanInfoList(loanInfo);
    }

    /**
     * 新增贷款信息
     *
     * @param loanInfo 贷款信息
     * @return 结果
     */
    @Override
    public int insertLoanInfo(LoanInfo loanInfo) {
        return loanInfoMapper.insertLoanInfo(loanInfo);
    }

    /**
     * 修改贷款信息
     *
     * @param loanInfo 贷款信息
     * @return 结果
     */
    @Override
    public int updateLoanInfo(LoanInfo loanInfo) {
        return loanInfoMapper.updateLoanInfo(loanInfo);
    }

    /**
     * 批量删除贷款信息
     *
     * @param ids 需要删除的贷款信息主键
     * @return 结果
     */
    @Override
    public int deleteLoanInfoByIds(Long[] ids) {
        return loanInfoMapper.deleteLoanInfoByIds(ids);
    }

    /**
     * 删除贷款信息信息
     *
     * @param id 贷款信息主键
     * @return 结果
     */
    @Override
    public int deleteLoanInfoById(Long id) {
        return loanInfoMapper.deleteLoanInfoById(id);
    }
}
