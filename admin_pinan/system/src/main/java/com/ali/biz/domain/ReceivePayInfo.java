package com.ali.biz.domain;

import com.ali.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 收款信息对象 biz_receive_pay_info
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@ApiModel(value = "收款信息 对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class ReceivePayInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 用户ID
     */
    @ApiModelProperty(name = "id", value = "用户ID")
    private Long id;

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "loanUserId", value = "用户ID")
    private Long loanUserId;

    @ApiModelProperty(name = "openName", value = "开户姓名")
    private String openName;

    /**
     * 开户银行
     */
    @ApiModelProperty(name = "openBank", value = "开户银行")
    private String openBank;

    /**
     * 银行卡号
     */
    @ApiModelProperty(name = "bankNumber", value = "银行卡号")
    private String bankNumber;

    /**
     * 开户地址
     */
    @ApiModelProperty(name = "openAddress", value = "开户地址")
    private String openAddress;

    /**
     * 图片信息
     */
    @ApiModelProperty(name = "imageInfo", value = "图片信息")
    private String imageInfo;

    /**
     * 状态（1贷款中 2已还款）
     */
    @ApiModelProperty(name = "status", value = "状态（1贷款中 2已还款）")
    private String status;

}
