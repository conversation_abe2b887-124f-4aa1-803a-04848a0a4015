package com.ali.biz.domain;

import com.ali.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 贷款用户对象 biz_loan_user
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@ApiModel(value = "贷款用户 对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class LoanUser extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 用户ID
     */
    @ApiModelProperty(name = "id", value = "用户ID")
    private Long id;

    /**
     * 用户账号
     */
    @ApiModelProperty(name = "userName", value = "用户账号")
    private String userName;

    /**
     * 密码
     */
    @ApiModelProperty(name = "password", value = "密码")
    private String password;

    /**
     * 手机号码
     */
    @ApiModelProperty(name = "phoneNumber", value = "手机号码")
    private String phoneNumber;

    /**
     * 省份证号码
     */
    @ApiModelProperty(name = "idCard", value = "省份证号码")
    private String idCard;

    /**
     * 开户银行
     */
    @ApiModelProperty(name = "openBank", value = "开户银行")
    private String openBank;

    /**
     * 银行卡号
     */
    @ApiModelProperty(name = "bankNumber", value = "银行卡号")
    private String bankNumber;

    /**
     * 开户地址
     */
    @ApiModelProperty(name = "openAddress", value = "开户地址")
    private String openAddress;

    /**
     * 显示顺序
     */
    @ApiModelProperty(name = "sort", value = "显示顺序")
    private Long sort;

    /**
     * 帐号状态（1正常 2停用）
     */
    @ApiModelProperty(name = "status", value = "帐号状态（1正常 2停用）")
    private String status;

    /**
     * 用户类型（00系统用户，1.商户）
     */
    @ApiModelProperty(name = "userType", value = "用户类型（00系统用户，1.商户）")
    private String userType;

    /**
     * 最后登录IP
     */
    @ApiModelProperty(name = "loginIp", value = "最后登录IP")
    private String loginIp;

    /**
     * 最后登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "loginDate", value = "最后登录时间")
    private Date loginDate;


    private Long loanInfoId;
    private BigDecimal loanAmount;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    private BigDecimal expectAmount;
    private BigDecimal payableAmount;
    private Integer loanInfoStatus;
    private Long payInfoId;
    private String payOpenName;
    private String payOpenBank;
    private String payBankNumber;
    private String payOpenAddress;
    private String imageInfo;
    private String payRemark;
}
