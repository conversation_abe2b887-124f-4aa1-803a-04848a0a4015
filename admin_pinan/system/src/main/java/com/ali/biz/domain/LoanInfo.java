package com.ali.biz.domain;

import com.ali.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 贷款信息对象 biz_loan_info
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@ApiModel(value = "贷款信息 对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class LoanInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 用户ID
     */
    @ApiModelProperty(name = "id", value = "用户ID")
    private Long id;

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "loanUserId", value = "用户ID")
    private Long loanUserId;

    /**
     * 贷款金额
     */
    @ApiModelProperty(name = "loanAmount", value = "贷款金额")
    private BigDecimal loanAmount;

    /**
     * 贷款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "startTime", value = "贷款日期")
    private Date startTime;

    /**
     * 还款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "endTime", value = "还款日期")
    private Date endTime;

    /**
     * 逾期金额
     */
    @ApiModelProperty(name = "expectAmount", value = "逾期金额")
    private BigDecimal expectAmount;

    /**
     * 应还金额
     */
    @ApiModelProperty(name = "payableAmount", value = "应还金额")
    private BigDecimal payableAmount;

    /**
     * 状态（1贷款中 2已还款）
     */
    @ApiModelProperty(name = "status", value = "状态（1贷款中 2已还款）")
    private String status;

}
