package com.ali.biz.service.impl;

import com.ali.biz.domain.ReceivePayInfo;
import com.ali.biz.mapper.ReceivePayInfoMapper;
import com.ali.biz.service.IReceivePayInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 收款信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Service
public class ReceivePayInfoServiceImpl implements IReceivePayInfoService {
    @Autowired
    private ReceivePayInfoMapper receivePayInfoMapper;

    /**
     * 查询收款信息
     *
     * @param id 收款信息主键
     * @return 收款信息
     */
    @Override
    public ReceivePayInfo selectReceivePayInfoById(Long id) {
        return receivePayInfoMapper.selectReceivePayInfoById(id);
    }

    /**
     * 查询收款信息列表
     *
     * @param receivePayInfo 收款信息
     * @return 收款信息
     */
    @Override
    public List<ReceivePayInfo> selectReceivePayInfoList(ReceivePayInfo receivePayInfo) {
        return receivePayInfoMapper.selectReceivePayInfoList(receivePayInfo);
    }

    /**
     * 新增收款信息
     *
     * @param receivePayInfo 收款信息
     * @return 结果
     */
    @Override
    public int insertReceivePayInfo(ReceivePayInfo receivePayInfo) {
        return receivePayInfoMapper.insertReceivePayInfo(receivePayInfo);
    }

    /**
     * 修改收款信息
     *
     * @param receivePayInfo 收款信息
     * @return 结果
     */
    @Override
    public int updateReceivePayInfo(ReceivePayInfo receivePayInfo) {
        return receivePayInfoMapper.updateReceivePayInfo(receivePayInfo);
    }

    /**
     * 批量删除收款信息
     *
     * @param ids 需要删除的收款信息主键
     * @return 结果
     */
    @Override
    public int deleteReceivePayInfoByIds(Long[] ids) {
        return receivePayInfoMapper.deleteReceivePayInfoByIds(ids);
    }

    /**
     * 删除收款信息信息
     *
     * @param id 收款信息主键
     * @return 结果
     */
    @Override
    public int deleteReceivePayInfoById(Long id) {
        return receivePayInfoMapper.deleteReceivePayInfoById(id);
    }
}
