package com.ali.biz.service;

import com.ali.biz.domain.LoanInfo;

import java.util.List;

/**
 * 贷款信息Service接口
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface ILoanInfoService {
    /**
     * 查询贷款信息
     *
     * @param id 贷款信息主键
     * @return 贷款信息
     */
    LoanInfo selectLoanInfoById(Long id);

    /**
     * 查询贷款信息列表
     *
     * @param loanInfo 贷款信息
     * @return 贷款信息集合
     */
    List<LoanInfo> selectLoanInfoList(LoanInfo loanInfo);

    /**
     * 新增贷款信息
     *
     * @param loanInfo 贷款信息
     * @return 结果
     */
    int insertLoanInfo(LoanInfo loanInfo);

    /**
     * 修改贷款信息
     *
     * @param loanInfo 贷款信息
     * @return 结果
     */
    int updateLoanInfo(LoanInfo loanInfo);

    /**
     * 批量删除贷款信息
     *
     * @param ids 需要删除的贷款信息主键集合
     * @return 结果
     */
    int deleteLoanInfoByIds(Long[] ids);

    /**
     * 删除贷款信息信息
     *
     * @param id 贷款信息主键
     * @return 结果
     */
    int deleteLoanInfoById(Long id);
}
