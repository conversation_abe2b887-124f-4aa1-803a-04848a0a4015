package com.ali.biz.mapper;

import com.ali.biz.domain.ReceivePayInfo;

import java.util.List;

/**
 * 收款信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface ReceivePayInfoMapper {
    /**
     * 查询收款信息
     *
     * @param id 收款信息主键
     * @return 收款信息
     */
    ReceivePayInfo selectReceivePayInfoById(Long id);

    /**
     * 查询收款信息列表
     *
     * @param receivePayInfo 收款信息
     * @return 收款信息集合
     */
    List<ReceivePayInfo> selectReceivePayInfoList(ReceivePayInfo receivePayInfo);

    /**
     * 新增收款信息
     *
     * @param receivePayInfo 收款信息
     * @return 结果
     */
    int insertReceivePayInfo(ReceivePayInfo receivePayInfo);

    /**
     * 修改收款信息
     *
     * @param receivePayInfo 收款信息
     * @return 结果
     */
    int updateReceivePayInfo(ReceivePayInfo receivePayInfo);

    /**
     * 删除收款信息
     *
     * @param id 收款信息主键
     * @return 结果
     */
    int deleteReceivePayInfoById(Long id);

    /**
     * 批量删除收款信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteReceivePayInfoByIds(Long[] ids);
}
