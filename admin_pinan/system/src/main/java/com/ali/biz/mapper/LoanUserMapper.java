package com.ali.biz.mapper;

import com.ali.biz.domain.LoanUser;

import java.util.List;

/**
 * 贷款用户Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface LoanUserMapper {
    /**
     * 查询贷款用户
     *
     * @param id 贷款用户主键
     * @return 贷款用户
     */
    LoanUser selectLoanUserById(Long id);

    /**
     * 查询贷款用户列表
     *
     * @param loanUser 贷款用户
     * @return 贷款用户集合
     */
    List<LoanUser> selectLoanUserList(LoanUser loanUser);

    /**
     * 新增贷款用户
     *
     * @param loanUser 贷款用户
     * @return 结果
     */
    int insertLoanUser(LoanUser loanUser);

    /**
     * 修改贷款用户
     *
     * @param loanUser 贷款用户
     * @return 结果
     */
    int updateLoanUser(LoanUser loanUser);

    /**
     * 删除贷款用户
     *
     * @param id 贷款用户主键
     * @return 结果
     */
    int deleteLoanUserById(Long id);

    /**
     * 批量删除贷款用户
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteLoanUserByIds(Long[] ids);

    LoanUser selectLoanUserByPhone(String phoneNumber);
}
