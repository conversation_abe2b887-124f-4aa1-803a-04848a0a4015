#!/bin/bash

# Nginx 配置管理脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置文件路径
NGINX_DIR="docker/nginx"
CONF_DIR="$NGINX_DIR/conf.d"
DEFAULT_CONF="$CONF_DIR/default.conf"
DEV_CONF="$CONF_DIR/default.dev.conf"
PROD_CONF="$CONF_DIR/default.prod.conf"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Nginx 配置管理脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  switch <env>    切换到指定环境配置 (dev|prod|default)"
    echo "  validate        验证当前配置文件语法"
    echo "  backup          备份当前配置"
    echo "  restore <file>  恢复配置文件"
    echo "  diff <env>      比较当前配置与指定环境配置的差异"
    echo "  reload          重新加载 Nginx 配置（需要容器运行）"
    echo "  help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 switch dev       # 切换到开发环境配置"
    echo "  $0 switch prod      # 切换到生产环境配置"
    echo "  $0 validate         # 验证配置语法"
    echo "  $0 backup           # 备份当前配置"
    echo "  $0 diff prod        # 比较当前配置与生产环境配置"
}

# 检查文件是否存在
check_file() {
    local file="$1"
    if [ ! -f "$file" ]; then
        log_error "文件不存在: $file"
        return 1
    fi
    return 0
}

# 切换配置环境
switch_config() {
    local env="$1"
    
    if [ -z "$env" ]; then
        log_error "请指定环境: dev, prod, 或 default"
        return 1
    fi
    
    case "$env" in
        "dev")
            local source_conf="$DEV_CONF"
            ;;
        "prod")
            local source_conf="$PROD_CONF"
            ;;
        "default")
            log_info "使用默认配置"
            return 0
            ;;
        *)
            log_error "不支持的环境: $env (支持: dev, prod, default)"
            return 1
            ;;
    esac
    
    if ! check_file "$source_conf"; then
        return 1
    fi
    
    log_step "切换到 $env 环境配置..."
    
    # 备份当前配置
    if [ -f "$DEFAULT_CONF" ]; then
        local backup_file="$DEFAULT_CONF.backup.$(date +%Y%m%d_%H%M%S)"
        cp "$DEFAULT_CONF" "$backup_file"
        log_info "当前配置已备份到: $backup_file"
    fi
    
    # 复制新配置
    cp "$source_conf" "$DEFAULT_CONF"
    log_info "已切换到 $env 环境配置"
    
    # 验证配置
    validate_config
}

# 验证配置文件语法
validate_config() {
    log_step "验证 Nginx 配置语法..."
    
    if ! check_file "$DEFAULT_CONF"; then
        return 1
    fi
    
    # 使用 Docker 容器验证配置
    if docker run --rm -v "$(pwd)/$NGINX_DIR/nginx.conf:/etc/nginx/nginx.conf:ro" \
                       -v "$(pwd)/$DEFAULT_CONF:/etc/nginx/conf.d/default.conf:ro" \
                       nginx:1.25-alpine nginx -t 2>/dev/null; then
        log_info "✅ Nginx 配置语法正确"
        return 0
    else
        log_error "❌ Nginx 配置语法错误"
        return 1
    fi
}

# 备份配置文件
backup_config() {
    log_step "备份 Nginx 配置..."
    
    local backup_dir="backups/nginx"
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="$backup_dir/nginx_config_$timestamp.tar.gz"
    
    mkdir -p "$backup_dir"
    
    tar -czf "$backup_file" -C "$NGINX_DIR" .
    
    log_info "配置已备份到: $backup_file"
}

# 恢复配置文件
restore_config() {
    local backup_file="$1"
    
    if [ -z "$backup_file" ]; then
        log_error "请指定备份文件"
        return 1
    fi
    
    if ! check_file "$backup_file"; then
        return 1
    fi
    
    log_step "恢复 Nginx 配置..."
    log_warn "这将覆盖当前配置，确认继续？(y/N)"
    read -r confirm
    
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        log_info "操作已取消"
        return 0
    fi
    
    # 备份当前配置
    backup_config
    
    # 恢复配置
    tar -xzf "$backup_file" -C "$NGINX_DIR"
    
    log_info "配置已恢复"
    
    # 验证配置
    validate_config
}

# 比较配置差异
diff_config() {
    local env="$1"
    
    if [ -z "$env" ]; then
        log_error "请指定要比较的环境: dev, prod"
        return 1
    fi
    
    case "$env" in
        "dev")
            local compare_conf="$DEV_CONF"
            ;;
        "prod")
            local compare_conf="$PROD_CONF"
            ;;
        *)
            log_error "不支持的环境: $env (支持: dev, prod)"
            return 1
            ;;
    esac
    
    if ! check_file "$DEFAULT_CONF" || ! check_file "$compare_conf"; then
        return 1
    fi
    
    log_step "比较当前配置与 $env 环境配置的差异..."
    
    if diff -u "$DEFAULT_CONF" "$compare_conf"; then
        log_info "配置文件相同"
    else
        log_info "以上是配置差异 (- 当前配置, + $env 环境配置)"
    fi
}

# 重新加载 Nginx 配置
reload_config() {
    log_step "重新加载 Nginx 配置..."
    
    # 检查容器是否运行
    if ! docker-compose ps frontend | grep -q "Up"; then
        log_error "前端容器未运行，请先启动服务"
        return 1
    fi
    
    # 验证配置
    if ! validate_config; then
        log_error "配置验证失败，取消重新加载"
        return 1
    fi
    
    # 重新加载配置
    if docker-compose exec frontend nginx -s reload; then
        log_info "✅ Nginx 配置重新加载成功"
    else
        log_error "❌ Nginx 配置重新加载失败"
        return 1
    fi
}

# 主函数
main() {
    local command="$1"
    
    case "$command" in
        "switch")
            switch_config "$2"
            ;;
        "validate")
            validate_config
            ;;
        "backup")
            backup_config
            ;;
        "restore")
            restore_config "$2"
            ;;
        "diff")
            diff_config "$2"
            ;;
        "reload")
            reload_config
            ;;
        "help"|"--help"|"-h"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
