#!/bin/bash

# admin_pinan 环境检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查结果
CHECKS_PASSED=0
CHECKS_FAILED=0

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
    ((CHECKS_PASSED++))
}

log_fail() {
    echo -e "${RED}[✗]${NC} $1"
    ((CHECKS_FAILED++))
}

# 检查命令是否存在
check_command() {
    local cmd="$1"
    local name="$2"
    
    if command -v "$cmd" &> /dev/null; then
        local version=$($cmd --version 2>&1 | head -n1)
        log_success "$name 已安装: $version"
        return 0
    else
        log_fail "$name 未安装或未在 PATH 中"
        return 1
    fi
}

# 检查 Docker 环境
check_docker_env() {
    log_step "检查 Docker 环境..."
    
    # 检查 Docker 命令
    if check_command "docker" "Docker"; then
        # 检查 Docker 服务状态
        if docker info &> /dev/null; then
            log_success "Docker 服务正在运行"
        else
            log_fail "Docker 服务未运行"
        fi
        
        # 检查 Docker 版本
        local docker_version=$(docker version --format '{{.Server.Version}}' 2>/dev/null)
        if [ -n "$docker_version" ]; then
            log_success "Docker 版本: $docker_version"
        fi
    fi
    
    # 检查 Docker Compose
    check_command "docker-compose" "Docker Compose"
}

# 检查系统资源
check_system_resources() {
    log_step "检查系统资源..."
    
    # 检查内存
    local total_mem=$(free -m | awk 'NR==2{printf "%.1f", $2/1024}')
    if (( $(echo "$total_mem >= 4.0" | bc -l) )); then
        log_success "系统内存: ${total_mem}GB (推荐 4GB+)"
    else
        log_warn "系统内存: ${total_mem}GB (推荐 4GB+)"
    fi
    
    # 检查磁盘空间
    local disk_space=$(df -h . | awk 'NR==2 {print $4}')
    log_info "可用磁盘空间: $disk_space"
    
    # 检查 CPU 核心数
    local cpu_cores=$(nproc)
    if [ "$cpu_cores" -ge 2 ]; then
        log_success "CPU 核心数: $cpu_cores (推荐 2+)"
    else
        log_warn "CPU 核心数: $cpu_cores (推荐 2+)"
    fi
}

# 检查网络连接
check_network() {
    log_step "检查网络连接..."
    
    # 检查端口占用
    local ports=(80 8088 3306 6379)
    for port in "${ports[@]}"; do
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            log_warn "端口 $port 已被占用"
        else
            log_success "端口 $port 可用"
        fi
    done
    
    # 检查 Docker 网络
    if docker network ls &> /dev/null; then
        log_success "Docker 网络功能正常"
    else
        log_fail "Docker 网络功能异常"
    fi
}

# 检查项目文件
check_project_files() {
    log_step "检查项目文件..."
    
    local required_files=(
        "docker-compose.yml"
        "admin_pinan/pom.xml"
        "web_pinan/package.json"
        "admin_pinan/sql/base_sql.sql"
        "docker/backend/Dockerfile"
        "docker/frontend/Dockerfile"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "文件存在: $file"
        else
            log_fail "文件缺失: $file"
        fi
    done
    
    # 检查目录结构
    local required_dirs=(
        "admin_pinan"
        "web_pinan"
        "docker"
        "scripts"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [ -d "$dir" ]; then
            log_success "目录存在: $dir"
        else
            log_fail "目录缺失: $dir"
        fi
    done
}

# 检查 WSL2 环境（如果适用）
check_wsl2() {
    if [ -n "$WSL_DISTRO_NAME" ]; then
        log_step "检查 WSL2 环境..."
        
        log_success "运行在 WSL2 环境: $WSL_DISTRO_NAME"
        
        # 检查 WSL 版本
        local wsl_version=$(cat /proc/version | grep -i microsoft)
        if [ -n "$wsl_version" ]; then
            log_success "WSL 内核版本正常"
        fi
        
        # 检查文件系统
        local fs_type=$(df -T . | awk 'NR==2 {print $2}')
        log_info "文件系统类型: $fs_type"
    else
        log_info "未检测到 WSL2 环境"
    fi
}

# 检查 Java 环境（可选）
check_java_env() {
    log_step "检查 Java 环境（可选）..."
    
    if check_command "java" "Java"; then
        local java_version=$(java -version 2>&1 | head -n1)
        log_info "Java 版本: $java_version"
    else
        log_info "Java 未安装（Docker 构建时会自动安装）"
    fi
    
    if check_command "mvn" "Maven"; then
        local mvn_version=$(mvn -version 2>&1 | head -n1)
        log_info "Maven 版本: $mvn_version"
    else
        log_info "Maven 未安装（Docker 构建时会自动安装）"
    fi
}

# 检查 Node.js 环境（可选）
check_node_env() {
    log_step "检查 Node.js 环境（可选）..."
    
    if check_command "node" "Node.js"; then
        local node_version=$(node --version)
        log_info "Node.js 版本: $node_version"
    else
        log_info "Node.js 未安装（Docker 构建时会自动安装）"
    fi
    
    if check_command "npm" "npm"; then
        local npm_version=$(npm --version)
        log_info "npm 版本: $npm_version"
    else
        log_info "npm 未安装（Docker 构建时会自动安装）"
    fi
}

# 生成报告
generate_report() {
    echo ""
    echo "=========================================="
    echo "           环境检查报告"
    echo "=========================================="
    echo ""
    echo "检查通过: $CHECKS_PASSED"
    echo "检查失败: $CHECKS_FAILED"
    echo ""
    
    if [ $CHECKS_FAILED -eq 0 ]; then
        log_success "环境检查全部通过，可以开始部署！"
        echo ""
        echo "下一步操作："
        echo "1. 运行 ./scripts/start.sh 开始部署"
        echo "2. 等待服务启动完成"
        echo "3. 访问 http://localhost 使用系统"
    else
        log_warn "发现 $CHECKS_FAILED 个问题，请解决后再进行部署"
        echo ""
        echo "常见解决方案："
        echo "1. 安装 Docker Desktop for Windows"
        echo "2. 启用 WSL2 集成"
        echo "3. 确保 Docker 服务正在运行"
        echo "4. 检查项目文件完整性"
    fi
    
    echo ""
    echo "=========================================="
}

# 主函数
main() {
    echo "admin_pinan 环境检查工具"
    echo "适用于 Windows Docker Desktop + WSL2 环境"
    echo ""
    
    check_docker_env
    check_system_resources
    check_network
    check_project_files
    check_wsl2
    check_java_env
    check_node_env
    
    generate_report
}

# 执行主函数
main "$@"
