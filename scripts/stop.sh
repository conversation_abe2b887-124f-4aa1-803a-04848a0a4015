#!/bin/bash

# admin_pinan 系统停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 停止服务
stop_services() {
    log_step "停止 admin_pinan 服务..."
    
    if [ -f "docker-compose.yml" ]; then
        docker-compose down
        log_info "服务已停止"
    else
        log_error "未找到 docker-compose.yml 文件"
        exit 1
    fi
}

# 清理资源（可选）
cleanup_resources() {
    if [ "$1" = "--cleanup" ]; then
        log_step "清理 Docker 资源..."
        
        # 删除容器
        docker-compose down --remove-orphans
        
        # 删除镜像
        log_warn "删除相关 Docker 镜像..."
        docker images | grep admin_pinan | awk '{print $3}' | xargs -r docker rmi -f
        
        # 删除数据卷（谨慎操作）
        read -p "是否删除数据卷？这将删除所有数据库数据 (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose down -v
            log_warn "数据卷已删除"
        fi
        
        log_info "资源清理完成"
    fi
}

# 显示状态
show_status() {
    log_step "显示容器状态..."
    docker-compose ps
}

# 主函数
main() {
    log_info "停止 admin_pinan 金融贷款管理系统"
    
    stop_services
    cleanup_resources "$1"
    show_status
    
    log_info "操作完成！"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --cleanup    停止服务并清理 Docker 资源"
    echo "  --help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0           # 仅停止服务"
    echo "  $0 --cleanup # 停止服务并清理资源"
}

# 处理命令行参数
case "$1" in
    --help)
        show_help
        exit 0
        ;;
    *)
        main "$1"
        ;;
esac
