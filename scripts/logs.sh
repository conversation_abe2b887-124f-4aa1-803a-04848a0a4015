#!/bin/bash

# admin_pinan 系统日志查看脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [服务名] [选项]"
    echo ""
    echo "服务名:"
    echo "  backend    查看后端服务日志"
    echo "  frontend   查看前端服务日志"
    echo "  mysql      查看 MySQL 服务日志"
    echo "  redis      查看 Redis 服务日志"
    echo "  all        查看所有服务日志"
    echo ""
    echo "选项:"
    echo "  -f, --follow    实时跟踪日志"
    echo "  -n, --lines     显示最后 N 行日志 (默认: 100)"
    echo "  --help          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 backend -f           # 实时查看后端日志"
    echo "  $0 mysql -n 50          # 查看 MySQL 最后 50 行日志"
    echo "  $0 all --follow         # 实时查看所有服务日志"
}

# 查看服务日志
view_logs() {
    local service="$1"
    local follow="$2"
    local lines="$3"
    
    if [ ! -f "docker-compose.yml" ]; then
        log_error "未找到 docker-compose.yml 文件"
        exit 1
    fi
    
    local cmd="docker-compose logs"
    
    if [ "$follow" = "true" ]; then
        cmd="$cmd -f"
    fi
    
    if [ -n "$lines" ]; then
        cmd="$cmd --tail=$lines"
    else
        cmd="$cmd --tail=100"
    fi
    
    case "$service" in
        backend)
            log_info "查看后端服务日志..."
            $cmd backend
            ;;
        frontend)
            log_info "查看前端服务日志..."
            $cmd frontend
            ;;
        mysql)
            log_info "查看 MySQL 服务日志..."
            $cmd mysql
            ;;
        redis)
            log_info "查看 Redis 服务日志..."
            $cmd redis
            ;;
        all)
            log_info "查看所有服务日志..."
            $cmd
            ;;
        *)
            log_error "未知的服务名: $service"
            show_help
            exit 1
            ;;
    esac
}

# 主函数
main() {
    local service=""
    local follow="false"
    local lines=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            backend|frontend|mysql|redis|all)
                service="$1"
                shift
                ;;
            -f|--follow)
                follow="true"
                shift
                ;;
            -n|--lines)
                lines="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定服务，默认查看所有服务
    if [ -z "$service" ]; then
        service="all"
    fi
    
    view_logs "$service" "$follow" "$lines"
}

# 执行主函数
main "$@"
