#!/bin/bash

# admin_pinan 系统启动脚本
# 适用于 Windows Docker Desktop + WSL2 环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查 Docker 环境
check_docker() {
    log_step "检查 Docker 环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或未在 PATH 中"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请启动 Docker Desktop"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "docker-compose 未安装"
        exit 1
    fi
    
    log_info "Docker 环境检查通过"
}

# 检查项目文件
check_project_files() {
    log_step "检查项目文件..."
    
    required_files=(
        "docker-compose.yml"
        "admin_pinan/pom.xml"
        "web_pinan/package.json"
        "admin_pinan/sql/base_sql.sql"
        "docker/backend/Dockerfile"
        "docker/frontend/Dockerfile"
        "docker/nginx/nginx.conf"
        "docker/nginx/conf.d/default.conf"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "缺少必需文件: $file"
            exit 1
        fi
    done
    
    log_info "项目文件检查通过"
}

# 配置 Nginx 环境
setup_nginx_config() {
    log_step "配置 Nginx 环境..."

    # 检查是否有 nginx-config.sh 脚本
    if [ -f "scripts/nginx-config.sh" ]; then
        chmod +x scripts/nginx-config.sh

        # 读取环境变量或使用默认值
        local nginx_env="${NGINX_CONFIG_ENV:-default}"

        if [ "$nginx_env" != "default" ]; then
            log_info "切换到 $nginx_env 环境的 Nginx 配置"
            ./scripts/nginx-config.sh switch "$nginx_env"
        else
            log_info "使用默认 Nginx 配置"
        fi

        # 验证配置
        if ./scripts/nginx-config.sh validate; then
            log_info "Nginx 配置验证通过"
        else
            log_warn "Nginx 配置验证失败，将使用默认配置"
        fi
    else
        log_warn "未找到 nginx-config.sh 脚本，跳过 Nginx 配置"
    fi
}

# 创建必要的目录
create_directories() {
    log_step "创建必要的目录..."
    
    directories=(
        "docker/mysql/data"
        "docker/redis/data"
        "logs/backend"
        "logs/nginx"
        "upload"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        log_info "创建目录: $dir"
    done
}

# 设置权限（WSL2 环境）
set_permissions() {
    log_step "设置文件权限..."
    
    # 设置脚本执行权限
    chmod +x scripts/*.sh
    
    # 设置日志目录权限
    chmod -R 755 logs/
    chmod -R 755 upload/
    
    log_info "权限设置完成"
}

# 构建和启动服务
start_services() {
    log_step "构建和启动服务..."
    
    # 停止现有服务
    log_info "停止现有服务..."
    docker-compose down --remove-orphans
    
    # 构建镜像
    log_info "构建 Docker 镜像..."
    docker-compose build --no-cache
    
    # 启动服务
    log_info "启动服务..."
    docker-compose up -d
    
    log_info "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_step "等待服务就绪..."
    
    # 等待 MySQL
    log_info "等待 MySQL 服务..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker-compose exec -T mysql mysqladmin ping -h localhost --silent; then
            log_info "MySQL 服务已就绪"
            break
        fi
        sleep 2
        ((timeout-=2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "MySQL 服务启动超时"
        exit 1
    fi
    
    # 等待 Redis
    log_info "等待 Redis 服务..."
    timeout=30
    while [ $timeout -gt 0 ]; do
        if docker-compose exec -T redis redis-cli ping | grep -q PONG; then
            log_info "Redis 服务已就绪"
            break
        fi
        sleep 2
        ((timeout-=2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "Redis 服务启动超时"
        exit 1
    fi
    
    # 等待后端服务
    log_info "等待后端服务..."
    timeout=120
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:8088/actuator/health &> /dev/null; then
            log_info "后端服务已就绪"
            break
        fi
        sleep 5
        ((timeout-=5))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "后端服务启动超时"
        exit 1
    fi
    
    # 等待前端服务
    log_info "等待前端服务..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost &> /dev/null; then
            log_info "前端服务已就绪"
            break
        fi
        sleep 2
        ((timeout-=2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "前端服务启动超时"
        exit 1
    fi
}

# 显示服务状态
show_status() {
    log_step "显示服务状态..."
    
    echo ""
    echo "=========================================="
    echo "           服务启动完成"
    echo "=========================================="
    echo ""
    echo "🌐 前端管理界面: http://localhost"
    echo "🔧 后端 API:     http://localhost:8088"
    echo "📊 Druid 监控:   http://localhost:8088/druid"
    echo "🗄️  MySQL:       localhost:3306"
    echo "🔴 Redis:        localhost:6379"
    echo ""
    echo "默认管理员账号:"
    echo "用户名: admin"
    echo "密码:   admin123"
    echo ""
    echo "=========================================="
    echo ""
    
    # 显示容器状态
    docker-compose ps
}

# 主函数
main() {
    log_info "开始部署 admin_pinan 金融贷款管理系统"
    
    check_docker
    check_project_files
    setup_nginx_config
    create_directories
    set_permissions
    start_services
    wait_for_services
    show_status
    
    log_info "部署完成！"
}

# 执行主函数
main "$@"
