<script setup>

import {get24thDate, numberToChinese} from "../../utils/utils";

const formData = uni.getStorageSync('userInfo')
</script>

<template>
  <view class="contract-container">
    <!-- 标题 -->
    <view class="contract-title">贷款合同</view>

    <!-- 正文内容 -->
    <view class="contract-body">
      <text>甲(出借人):平安贷有限公司</text>
      <text>乙(借款人):{{ formData.userName }}</text>
      <text>身份证:{{ formData.idCard }}</text>
      <text>手机号:{{ formData.phoneNumber }}</text>
      <text>
        甲乙双方本着平等自愿、诚实守信的原则,经协商一致,达成本小额贷款合同,并保证共同遵守执行。
      </text>

      <text class="subtitle">一、借款金额</text>
      <text class="paragraph">
        1. 乙方向甲方借款人(大写:人民币{{ numberToChinese(formData.loanAmount) }}元整元)(小写:{{ formData.loanAmount }}
        元)。
      </text>

      <text class="subtitle">二、借款利息</text>
      <text class="paragraph">1. 借款日利率0.0025。</text>

      <text class="subtitle">三、借款期限</text>
      <text class="paragraph">
        1. 借款期限为,从{{ formData.startTime }}起至 {{ get24thDate(formData.startTime) }}为第一
        个月还款日。如实际放款日与该日期不符,以实际
        借款日为准。乙方收到借款后应当出具收据,甲方
        所出具的借据为本小额贷款合同附件,与本小额贷
        款合同具有同等法律效力。
      </text>

      <text class="subtitle">四、保证条款:</text>
      <text class="paragraph">
        1. 借款方不得用借款进行违法活动。否则,甲方有权
        要求乙方立即还本付息,所产生的法律后果由乙方
        自负。
      </text>
      <text class="paragraph">
        2. 借款方必须按合同规定的定期还本付息。逾期不还
        的部分,借款方有权限追回借款并收取每天借款总
        金额的0.25%逾期费用。
      </text>

      <text class="subtitle">五、甲方以转账的方式将借款项打入乙方账户。</text>

      <text class="subtitle">六、违约责任</text>
      <text class="paragraph">
        1. 乙方如未按合同规定还款,乙方应当承担违约金以
        及因诉讼发生的律师、诉讼费、差旅费等费用。
      </text>
      <text class="paragraph">
        2. 当甲方认为借款人发生或可能发生影响偿还能力之
        情形时,甲方有权提前收回借款,借款人应及时返
        还,借款人及保证人不得以任何理由抗辩。
      </text>

      <text class="subtitle">七、合同争议的解决方式</text>
      <text class="paragraph">
        本合同在履行过程中发生的争议,由当事人双方友好
        协商解决,也可以由第三人调解。协商或调解不成
        的,可依法向甲方所在地人民法院提起诉讼。
      </text>

      <text class="paragraph">
        1. 乙方支付服务费后借款合同立即生效,如乙方放弃
        借款或是乙方造成的过错,导致下款失败,乙方自
        己承担与甲方无关(包含服务费)乙方需按时还
        款。
      </text>
      <text class="paragraph">
        2. 如乙方问题需要修改收款信息或解冻账户需收取一
        定的押金费用,根据通过的额度百分比计算收取。
      </text>
      <text class="paragraph">
        3. 提现之前乙方需支付(服务费=审批额度*0.05)待
        乙方还清2期借款后退款到乙方个人账户钱包。
      </text>
      <text class="paragraph">
        4. 本小额贷款合同自各方签字(含电子签名)之日起
        生效。合同文本具有同等法律效力。
      </text>

      <text class="subtitle">八、甲方责任</text>
      <text class="paragraph">
        1. 如乙方已支付服务费后,甲方问题超过5分钟不放
        款,乙方支付的5%服务费无条件全额原路退还。
      </text>
    </view>

    <!-- 签署区 -->
    <view class="signature-section">
      <view class="signature-text">
        <text>出借人: 平安贷有限公司</text>
        <text>借款人: {{ formData.userName }}</text>
        <text>签订日期: {{ formData.startTime }}</text>
      </view>
      <image src="/static/zh/htzh.png" class="seal-image"/>
    </view>
  </view>
</template>

<style scoped>
.contract-container {
  padding: 40rpx;
  background-color: #fff;
  font-family: "NSimSun", "Times New Roman", serif;
  line-height: 1.5;
}

/* 标题：华文中宋 小二号 下划线 */
.contract-title {
  text-align: center;
  font-family: "STZhongsong", serif;
  font-size: 36rpx;
  text-decoration: underline;
  margin-bottom: 20rpx;
}

/* 正文：新宋体 小四号 行距1.5倍 */
.contract-body {
  font-family: "NSimSun", "Times New Roman", serif;
  font-size: 28rpx;
  line-height: 1.5;
}

.contract-body text {
  display: block;
  margin-bottom: 15rpx;
}

/* 大标题保持不缩进 */
.subtitle {
  font-weight: bold;
}

/* 段落编号缩进一个空格 */
.paragraph {
  padding-left: 20rpx;
}

/* 签署区 */
.signature-section {
  margin-top: 40rpx;
  position: relative;
  padding-top: 40rpx;
}

/* 签署文字 */
.signature-text {
  font-family: "NSimSun", "Times New Roman", serif;
  font-size: 28rpx;
  text-align: left;
  line-height: 1.5;
  position: relative;
  z-index: 1;
}

.signature-text text {
  display: block;
  margin-bottom: 20rpx;
}

/* 漂浮印章 */
.seal-image {
  position: absolute;
  left: 40rpx;
  top: 20rpx;
  width: 200rpx;
  height: 200rpx;
  opacity: 0.7;
  z-index: 2;
  pointer-events: none;
}
</style>
