.page-container {
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 开启纵向滚动 */
  min-height: 122vh; /* 测试滚动条是否正常 */
}


/* 顶部滚动图片 */
.banner {
  width: 100%;
  height: 200px;
}

.banner-img {
  width: 100%;
  height: 100%;
}

/* 滚动消息 */
.notice-bar {
  display: flex;
  align-items: center;
  background-color: #fef6e0;
  padding: 10rpx;
}

.horn-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}

.notice-swiper {
  flex: 1;
  height: 40rpx;
  display: flex;
  text-align: center;
}

.notice-text {
  font-size: 14px;
  color: #333;
}

/* 四个图标按钮 */
.icon-row {
  display: flex;
  justify-content: space-around;
  padding: 50rpx 0;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.icon-img {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 8rpx;
}

.icon-circle {
  width: 100rpx; /* 圆形直径 */
  height: 100rpx;
  background-color: #fff;
  border-radius: 50%; /* 圆形 */
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1); /* 阴影让圆形更明显 */
  margin-bottom: 10rpx;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.icon-item text {
  font-size: 12px;
  color: #555;
}

/* 只读表单 */
.dh-text {
  margin: 10rpx 20rpx;
  font-weight: bold;
  color: darkgreen;
}

.readonly {
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  margin: 20rpx;
  padding: 20rpx;
}

.readonly-form {
  position: relative;
}

/* 倾斜水印样式 */
.status-watermark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-20deg);
  font-size: 40rpx;
  font-weight: bold;
  opacity: 0.28;
  white-space: nowrap;
}

.status-watermark.overdue {
  color: #e53935; /* 红色 */
}

.status-watermark.normal {
  color: #43a047; /* 绿色 */
}

.form-item {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  color: #333;
}

.value {
  font-weight: 500;
  color: $uni-primary;
}

/* 按钮区域 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}

.btn {
  flex: 1;
  text-align: center;
  font-size: 14px;
  line-height: 60rpx;
  border-radius: 8rpx;
  color: #fff;
  margin: 60rpx;
}

/* 按钮间隔 */
.btn + .btn {
  margin-left: 20rpx;
}

/* 具体颜色 */
.btn-detail {
  background-color: #007aff; /* 蓝色 */
}

.btn-repay {
  background-color: #f56c6c;
}

.btn-repayjK {
  background-color: #f9ae3d;
}