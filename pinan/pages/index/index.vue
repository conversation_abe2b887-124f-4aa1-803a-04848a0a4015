<script setup>
import {ref} from 'vue'
import {onShow} from '@dcloudio/uni-app'
const formData = ref({})
const alsoDay = ref(0)

onShow(() => {
  const data = uni.getStorageSync('userInfo') || {}
  formData.value = data
  if (data.endTime) {
    alsoDay.value = Math.ceil(
      (Date.now() - new Date(data.endTime).getTime()) / (1000 * 60 * 60 * 24)
    )
  } else {
    alsoDay.value = 0
  }
})

// 四个功能按钮配置
const icons = ref([
  {title: '借款', img: '/static/nav/nav-1.png', url: '/pages/borrow/borrow'},
  {title: '还款', img: '/static/nav/nav-2.png', url: '/pages/also/also'},
  {title: '隐私协议', img: '/static/nav/nav-3.png', url: '/pages/privacy/index'},
  {title: '帮助', img: '/static/nav/nav-4.png', url: '/pages/help/index'}
])

// 点击跳转
function goToPage(url) {
  if (url === '/pages/borrow/borrow' || url === '/pages/also/also') {
    uni.switchTab({url})
    return
  }
  uni.navigateTo({url})
}

// 查看详情
function viewDetail() {
  uni.switchTab({url: '/pages/also/also'})
}

// 立即还款
function repayNow() {
  uni.navigateTo({url: '/pages/alsoaccount/also'})
}

// 借款
function viewJKDetail() {
  uni.switchTab({url: '/pages/borrow/borrow'})
}

// 滚动消息
const messages = ref([
  '欢迎来到平安贷',
  '最新活动：限时优惠',
  '请注意保护个人信息'
])

</script>

<template>
  <view class="page-container">
    <!-- 顶部滚动图片 -->
    <swiper autoplay circular indicator-dots class="banner">
      <swiper-item v-for="(item, index) in [1,2,3,4]" :key="index">
        <image :src="`/static/banner/index-${item}.png`" mode="aspectFill" class="banner-img"/>
      </swiper-item>
    </swiper>

    <!-- 滚动消息 -->
    <view class="notice-bar">
      <image src="/static/horn.png" class="horn-icon"/>
      <swiper autoplay vertical circular interval="2000" class="notice-swiper">
        <swiper-item v-for="(msg, index) in messages" :key="index">
          <text class="notice-text">📣</text>
          <text class="notice-text">{{ msg }}</text>
        </swiper-item>
      </swiper>
    </view>

    <!-- 四个图片按钮 -->
    <view class="icon-row">
      <view
        class="icon-item"
        v-for="(item, index) in icons"
        :key="index"
        @click="goToPage(item.url)"
      >
        <view class="icon-circle">
          <image :src="item.img" mode="aspectFit" class="icon-img"/>
        </view>
        <text>{{ item.title }}</text>
      </view>
    </view>
    <view class="dh-text">
      待还款项
    </view>
    <!-- 只读表单 -->
    <view class="readonly">
      <view class="readonly-form">
        <!-- 倾斜水印 -->
        <view
          class="status-watermark"
          :class="formData.loanInfoStatus == 2 || alsoDay <= 0  ? 'normal':  'overdue'"
        >
          {{ formData.loanInfoStatus == 2 ? '已还款' : alsoDay > 0 ? '已逾期' : '正常' }}
        </view>
        <!-- 表单内容 -->
        <view class="form-item">
          <text class="label">贷款金额</text>
          <text class="value">{{ formData.loanAmount }}</text>
        </view>
        <view class="form-item">
          <text class="label">到期日期</text>
          <text class="value">{{ formData.endTime }}</text>
        </view>
        <view class="form-item">
          <text class="label">逾期天数</text>
          <text class="value">{{ alsoDay < 0 ? "未逾期" : alsoDay + '天' }}</text>
        </view>
        <view class="form-item">
          <text class="label">逾期金额</text>
          <text class="value">{{ formData.expectAmount }}</text>
        </view>
        <view class="form-item">
          <text class="label">应还金额</text>
          <text class="value">{{ formData.payableAmount }}</text>
        </view>
      </view>

      <!-- 按钮区域 -->
      <view class="action-buttons">
        <button class="btn btn-detail" @click="viewDetail">查看详情</button>
        <button class="btn btn-repay" @click="repayNow">立即还款</button>
      </view>
    </view>


    <view class="dh-text">
      我要借款
    </view>
    <!-- 只读表单 -->
    <view class="readonly">
      <view class="readonly-form">
        <!-- 表单内容 -->
        <view class="form-item">
          <text class="label">最多可借</text>
          <text class="value">{{ 200000 }}</text>
        </view>
        <view class="form-item">
          <text class="label">利率低至</text>
          <text class="value">{{ 23 }}%</text>
        </view>
        <view class="form-item">
          <text class="label">最长期限</text>
          <text class="value">{{ 365 }} 天</text>
        </view>
      </view>

      <!-- 按钮区域 -->
      <view class="action-buttons">
        <button class="btn btn-detail" @click="viewJKDetail">查看详情</button>
        <button class="btn btn-repayjK" @click="viewJKDetail">立即借款</button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import './index.scss';
</style>
