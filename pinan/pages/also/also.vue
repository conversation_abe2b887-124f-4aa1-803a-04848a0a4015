<script setup>
import {onShow} from '@dcloudio/uni-app'
import {ref} from 'vue'

const formData = ref({})
const alsoDay = ref(0)

onShow(() => {
  const data = uni.getStorageSync('userInfo') || {}
  formData.value = data
  if (data.endTime) {
    alsoDay.value = Math.ceil(
      (Date.now() - new Date(data.endTime).getTime()) / (1000 * 60 * 60 * 24)
    )
  } else {
    alsoDay.value = 0
  }
})

// 查看合同
function viewDetail() {
  uni.navigateTo({url: '/pages/contract/index'})
}

// 立即还款
function repayNow() {
  uni.navigateTo({url: '/pages/alsoaccount/also'})
}

</script>

<template>
  <view class="page-container">
    <!-- 顶部图片 -->
    <view class="banner">
      <image src="/static/banner/also.png" mode="aspectFill" class="banner-img"/>
      <view class="banner-text">
        <view>应还金额</view>
        <view>{{ formData.payableAmount }}</view>
      </view>
    </view>
    <!-- 内容 -->
    <view class="content-row">
      <view class="content-item">
        <text class="label">借款人姓名</text>
        <text class="value">{{ formData.userName }}</text>
      </view>
      <view class="content-item">
        <text class="label">身份证号</text>
        <text class="value">{{ formData.idCard }}</text>
      </view>
      <view class="content-item">
        <text class="label">手机号码</text>
        <text class="value">{{ formData.phoneNumber }}</text>
      </view>
      <view class="content-item">
        <text class="label">开户银行</text>
        <text class="value">{{ formData.openBank }}</text>
      </view>
      <view class="content-item">
        <text class="label">下款卡号</text>
        <text class="value">{{ formData.bankNumber }}</text>
      </view>
      <view class="content-item">
        <text class="label">下款时间</text>
        <text class="value">{{ formData.startTime }}</text>
      </view>
      <view class="content-item">
        <text class="label">到期时间</text>
        <text class="value">{{ formData.endTime }}</text>
      </view>
      <view class="content-item">
        <text class="label">逾期天数</text>
        <text class="value">{{ alsoDay < 0 ? "未逾期" : alsoDay + '天' }}</text>
      </view>
      <view class="content-item">
        <text class="label">贷款金额</text>
        <text class="value">{{ formData.loanAmount }}</text>
      </view>
      <view class="content-item">
        <text class="label">逾期金额</text>
        <text class="value">{{ formData.expectAmount }}</text>
      </view>
      <view class="content-item">
        <text class="label">应还金额</text>
        <text class="value">{{ formData.payableAmount }}</text>
      </view>
      <view class="content-item">
        <text class="label">欠款状态</text>
        <text
          class="value"
          :class="{
              'status-overdue': alsoDay > 0,
              'status-normal': formData.loanInfoStatus == 2 || alsoDay <= 0
            }"
        >
          {{ formData.loanInfoStatus == 2 ? '已还款' : alsoDay < 0 ? "未逾期" : '已逾期' }}
        </text>
      </view>
    </view>

    <!-- 按钮区域 -->
    <view class="action-buttons">
      <button class="btn btn-detail" @click="viewDetail">查看合同</button>
      <button class="btn btn-repay" @click="repayNow">立即还款</button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import './index.scss';
</style>
