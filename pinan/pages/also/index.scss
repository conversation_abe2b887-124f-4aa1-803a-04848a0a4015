.page-container {
  display: flex;
  flex-direction: column;
  min-height: 108vh;       /* 占满整个视口 */
}


/* 顶部图片 */
.banner {
  position: relative;
  width: 100%;
  height: 200px; // 根据实际图片高度调整
  .banner-img {
    width: 100%;
    height: 100%;
  }

  .banner-text {
    position: absolute;
    left: 25%; // 左半部分中心
    top: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-family: 'Helvetica Neue', Helvetica, sans-serif;

    view:first-child {
      font-size: 36rpx; // “最高可借额度”
    }

    view:last-child {
      font-size: 36rpx;
      margin-top: 20rpx;
    }
  }
}

/* 主要内容区域 */
.content-row {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  border-radius: 16rpx;
}

.content-item {
  display: flex;
  justify-content: flex-start; /* 两项都左对齐 */
  padding: 20rpx 20rpx;
  font-size: 28rpx;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  &:last-child {
    border-bottom: none;
  }

  .label {
    color: #666;
    width: 200rpx; /* 可选：固定宽度，使对齐整齐 */
  }

  .value {
    color: #333;
    margin-left: 20rpx;
  }

  /* 已逾期：红色边框 */
  .status-overdue {
    color: #ff0000;
    border: 2rpx solid #ff0000;
    padding: 2rpx 12rpx;
    border-radius: 8rpx;
  }

  /* 正常：绿色边框 */
  .status-normal {
    color: #00aa00;
    border: 2rpx solid #00aa00;
    padding: 2rpx 12rpx;
    border-radius: 8rpx;
  }
}

/* 按钮区域 */
.action-buttons {
  display: flex;
  justify-content: space-between;
}

.btn {
  flex: 1;
  text-align: center;
  font-size: 14px;
  line-height: 65rpx;
  border-radius: 8rpx;
  color: #fff;
  margin: 0 40rpx;
}

/* 按钮间隔 */
.btn + .btn {
  margin-left: 20rpx;
}

/* 具体颜色 */
.btn-detail {
  background-color: #007aff; /* 蓝色 */
}

.btn-repay {
  background-color: #f56c6c;
}
