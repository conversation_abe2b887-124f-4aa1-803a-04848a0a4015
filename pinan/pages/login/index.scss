.page-container {
  height: calc(100vh - 50px);
}

.user-profile {
  height: 500rpx;
  padding-top: env(safe-area-inset-top);
  background-image: url('/static/images/photo11.jpg');
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  line-height: 1.8;
  font-size: 30rpx;

  /* 只给底部圆角 */
  border-bottom-left-radius: 20rpx;
  border-bottom-right-radius: 20rpx;

  overflow: hidden; /* 确保背景图和内容也遵循圆角 */
}

.user-login {
  height: 600rpx;
  margin: 80px 40rpx;
  border-radius: 50rpx;
  background-color: #fff;
  position: relative;
  top: -220rpx;
}

.login-type {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  line-height: 1;

  .title {
    font-size: 48rpx;
    color: $uni-primary;
    text-align: center;
    margin: 60rpx auto;
  }
}

.login-content {
  margin: 0 60rpx;
  align-items: flex-end;
  justify-content: center;
}

.uni-forms-item {
  height: 100rpx;
  margin-bottom: 20rpx !important;
  border-bottom: 2rpx solid #eee;
  box-sizing: border-box;
}

.uni-input-input {
  border: 0.5rpx solid #ccc; /* 浅灰色边框 */
  border-radius: 20rpx; /* 圆角 */
  padding: 16rpx 20rpx; /* 内边距 */
  box-sizing: border-box; /* 盒模型 */
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1); /* 阴影 */
}

::v-deep .uni-forms-item__content {
  display: flex;
  align-items: center;
}

::v-deep input {
  width: 100%;
  font-size: $uni-font-size-base;
  color: $uni-main-color;
}

::v-deep .uni-forms-item__error {
  width: 100%;
  padding-top: 10rpx;
  border-top: 2rpx solid $uni-primary;
  color: $uni-primary;
  font-size: $uni-font-size-small;
  transition: none;
}

.submit-button {
  height: 80rpx;
  line-height: 80rpx;
  padding-top: 4rpx;
  margin-top: 60rpx;
  border: none;
  color: #fff;
  background-color: $uni-primary;
  border-radius: 100rpx;
  font-size: $uni-font-size-big;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  margin: 28rpx 0;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: $uni-font-size-small;
  color: #333;
}

.privacy-link {
  color: #f98a1d;
  margin-left: 4px;
}

.submit-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

