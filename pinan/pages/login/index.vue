<script setup>
import {ref} from 'vue'
import request from "../../utils/request";

const isChecked = ref(false)

const formData = uni.getStorageSync('userInfo')

const phone = ref(formData?.phoneNumber || '')

/**
 * 变更过checkbox状态
 * @param e
 */
function onChange(e) {
  isChecked.value = e.detail.value.length > 0
  console.log('checkbox change', e.detail.value)
  console.log(isChecked.value)
}

function validatePhone(phone) {
  // 先确保是字符串，长度为 11，再匹配正则
  return typeof phone === 'string' && phone.length === 11 && /^1[3-9]\d{9}$/.test(phone);
}


/**
 * 隐私政策
 */
function openPrivacy() {
  uni.navigateTo({url: '/pages/privacy/index'})
}

// 登录按钮点击事件
function handleLogin() {
  if (!phone.value) {
    uni.showToast({title: '请输入手机号', icon: 'none'})
    return
  }
  if (!isChecked.value) {
    uni.showToast({title: '请先同意隐私政策', icon: 'none'})
    return
  }
  if (!validatePhone(phone.value)) {
    uni.showToast({title: '手机号码格式错误', icon: 'none'})
    return
  }
  // 登录接口，noAuth: true 表示不需要 token
  request.post('/h5Login', {username: phone.value, password: phone.value}, {noAuth: true})
    .then(res => {
      uni.setStorageSync('token', res.data)
      // 登录接口，noAuth: true 表示不需要 token
      request.get('/h5GetInfo', {})
        .then(res => {
          uni.setStorageSync('userInfo', res.data)
          // 跳转到首页
          uni.switchTab({url: '/pages/index/index'})
          console.log('登录成功', res);
        })
        .catch(err => {

        });

    })
    .catch(err => {
      console.error('登录失败', err);
    });
}

</script>
<template>
  <view class="page-container">
    <view class="user-profile"/>
    <view class="user-login">
      <view class="login-type">
        <view class="title">平安贷</view>
      </view>
      <view class="login-content">
        <input
          v-model="phone"
          type="text"
          placeholder="请输入手机号码"
          class="uni-input-input"
        />

        <view class="checkbox-wrapper">
          <checkbox-group @change="onChange">
            <label class="checkbox-label">
              <checkbox :checked="isChecked" color="#f98a1d" style="transform: scale(0.8);"/>
              <text>我已阅读并同意</text>
              <text class="privacy-link" @click="openPrivacy">《隐私政策》</text>
            </label>
          </checkbox-group>
        </view>
        <button class="submit-button" @click="handleLogin">登 录</button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import './index.scss';
</style>
