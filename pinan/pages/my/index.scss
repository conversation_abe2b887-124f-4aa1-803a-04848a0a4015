.page-container {
  height: calc(100vh - 50px);
}

.user-profile {
  display: flex;
  align-items: center;
  padding: 40rpx 20rpx 20rpx 40rpx;
}

.avatar {
  width: 200rpx; /* 头像大小 */
  height: 200rpx;
  border-radius: 50%; /* 圆形头像 */
  margin-right: 30rpx;
}

.user-info {
  display: flex;
  flex-direction: column; /* 垂直排列 */
}

.username {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.no {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-top: 8rpx;
}

.content-row {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  border-radius: 16rpx;
}

.content-item {
  display: flex;
  align-items: center; /* 垂直居中 */
  padding: 20rpx 20rpx;
  font-size: 28rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 16rpx;

  &:last-child {
    border-bottom: none;
  }
  .content-img{
    width: 60rpx; /* 可选：图片大小 */
    height: 60rpx;
    margin-right: 20rpx; /* 图片与文字间距 */
  }

  .label {
    color: #666;
    width: 200rpx; /* 可选：固定宽度，使对齐整齐 */
  }

  .value {
    color: #333;
    margin-left: 28rpx;
    //margin: 0 auto;
  }
}


.btn {
  flex: 1;
  text-align: center;
  font-size: 14px;
  line-height: 60rpx;
  border-radius: 10rpx;
  color: #fff;
  margin: 0 30rpx;
}

.btn-repay {
  background-color: #f56c6c;
}
