<script setup>
// 退出登录
function logOut() {
  // 删除缓存  uni.removeStorageSync('token')
  uni.removeStorageSync('token')
  // 跳转到登录页
  uni.navigateTo({url: '/pages/login/index'})
}

const formData = uni.getStorageSync('userInfo')
// 点击跳转
function goToPage(url) {
  if (url === '/pages/borrow/borrow' || url === '/pages/also/also') {
    uni.switchTab({url})
    return
  }
  uni.navigateTo({url})
}
</script>

<template>
  <view class="page-container">
    <view class="user-profile">
      <image class="avatar" src="/static/nav/toux.png" mode="平安贷"/>
      <view class="user-info">
        <text class="username">{{formData.userName}}</text>
        <text class="no">{{formData.phoneNumber}}</text>
      </view>
    </view>
    <!-- 内容 -->
    <view class="content-row">
      <view class="content-item" @click="goToPage('/pages/privacy/index')">
        <image class="content-img" src="/static/nav/borrow-1.png" mode="平安贷"/>
        <text class="value">
          隐私协议
        </text>
      </view>

      <view class="content-item" @click="goToPage('/pages/help/index')">
        <image class="content-img" src="/static/nav/borrow-2.png" mode="平安贷"/>
        <text class="value">
          客户须知
        </text>
      </view>

      <view class="content-item" @click="goToPage('/pages/contract/index')">
        <image class="content-img" src="/static/nav/nav-4.png" mode="平安贷"/>
        <text class="value">
          贷款合同
        </text>
      </view>
      <view class="content-item" @click="goToPage('/pages/userinfo/index')">
        <image class="content-img" src="/static/nav/nav-3.png" mode="平安贷"/>
        <text class="value">
          个人信息
        </text>
      </view>
    </view>

    <!-- 按钮区域 -->
    <view class="action-buttons">
      <button class="btn btn-repay" @click="logOut">退出登录</button>
    </view>
  </view>

</template>

<style lang="scss" scoped>
@import './index.scss';
</style>
