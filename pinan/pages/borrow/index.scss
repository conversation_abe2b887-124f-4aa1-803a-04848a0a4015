.page-container {
  display: flex;
  flex-direction: column;
}


/* 顶部图片 */
.banner {
  position: relative;
  width: 100%;
  height: 200px; // 根据实际图片高度调整
  .banner-img {
    width: 100%;
    height: 100%;
  }

  .banner-text {
    position: absolute;
    left: 25%; // 左半部分中心
    top: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-family: 'Helvetica Neue', Helvetica, sans-serif;

    view:first-child {
      font-size: 36rpx; // “最高可借额度”
    }

    view:last-child {
      font-size: 36rpx;
      margin-top: 20rpx;
    }
  }
}

/* 四个图标按钮 */
.icon-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 40rpx 20rpx; /* 左右留白 */
  box-sizing: border-box;
}

.icon-item {
  width: 48%; /* 每行两个 */
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
  background-color: #fff; /* 白色背景 */
  border-radius: 16rpx; /* 可选：圆角 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1); /* 可选：阴影 */
  padding: 40rpx 0; /* 内边距 */
}

.icon-circle {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-img {
  width: 80%;
  height: 80%;
}

.icon-text {
  margin-top: 10rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  font-family: 'Helvetica Neue', Helvetica, sans-serif;

  text {
    font-size: 26rpx;
    color: #333;
    line-height: 36rpx;
  }

  .title-line1 {
    color: #ff6868;
    margin-bottom: 10rpx;
  }
}

.btn {
  flex: 1;
  text-align: center;
  font-size: 14px;
  line-height: 60rpx;
  border-radius: 10rpx;
  color: #fff;
  margin: 0 30rpx;
}

.btn-repay {
  background-color: #f9ae3d;
}