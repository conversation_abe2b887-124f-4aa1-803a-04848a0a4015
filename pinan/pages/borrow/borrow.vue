<script setup>
import {ref} from 'vue'

// 按钮配置
const icons = ref([
  {titleLine1: '200000', titleLine2: '最高可借', img: '/static/nav/borrow-1.png'},
  {titleLine1: '0.25%', titleLine2: '利率低至', img: '/static/nav/borrow-2.png'},
  {titleLine1: '期限最长', titleLine2: '最长可至24个月', img: '/static/nav/nav-3.png'},
  {titleLine1: '灵活还款', titleLine2: '支付宝/微信/银行卡', img: '/static/nav/borrow-4.png'}
])

// 立即 借款
function repayNow() {
  uni.showToast({title: '逾期暂不开放', icon: 'none'})
}
</script>

<template>
  <view class="page-container">
    <!-- 顶部滚动图片 -->
    <view class="banner">
      <image src="/static/banner/borrow.png" mode="aspectFill" class="banner-img"/>
      <view class="banner-text">
        <view>最高可借额度</view>
        <view>200000</view>
      </view>
    </view>


    <view class="icon-row">
      <view
        class="icon-item"
        v-for="(item, index) in icons"
        :key="index"
      >
        <view class="icon-circle">
          <image :src="item.img" mode="aspectFit" class="icon-img"/>
        </view>
        <view class="icon-text">
          <text class="title-line1">{{ item.titleLine1 }}</text>
          <text>{{ item.titleLine2 }}</text>
        </view>
      </view>
    </view>
    <!-- 按钮区域 -->
    <view class="action-buttons">
      <button class="btn btn-repay" @click="repayNow">立即借款</button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import './index.scss';
</style>
