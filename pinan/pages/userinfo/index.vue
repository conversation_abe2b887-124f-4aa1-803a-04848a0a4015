<script setup>
import {ref} from 'vue'
const formData = uni.getStorageSync('userInfo')

</script>

<template>
  <view class="page-container">
    <view class="user-profile">
<!--      <image class="avatar" src="/static/nav/toux.png" mode="平安贷"/>-->
      <view class="user-info">
        <text class="username">{{formData.userName.charAt(0)}}</text>
      </view>
    </view>
    <!-- 内容 -->
    <view class="content-row">
      <view class="content-item">
        <text class="label">姓名</text>
        <text class="value">{{formData.userName}}</text>
      </view>
      <view class="content-item">
        <text class="label">身份证号</text>
        <text class="value">{{formData.idCard}}</text>
      </view>
      <view class="content-item">
        <text class="label">手机号码</text>
        <text class="value">{{formData.phoneNumber}}</text>
      </view>
      <view class="content-item">
        <text class="label">开户银行</text>
        <text class="value">{{formData.openBank}}</text>
      </view>
      <view class="content-item">
        <text class="label">银行卡号</text>
        <text class="value">{{formData.bankNumber}}</text>
      </view>

    </view>
  </view>

</template>

<style lang="scss" scoped>
@import './index.scss';
</style>
