.page-container {
  height: calc(100vh - 50px);
}

.user-profile {
  height: 500rpx;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  background-image: url('/static/images/photo22.jpg');
  /* 只给底部圆角 */
  border-bottom-left-radius: 20rpx;
  border-bottom-right-radius: 20rpx;

  overflow: hidden; /* 确保背景图和内容也遵循圆角 */
}

.avatar {
  width: 200rpx; /* 头像大小 */
  height: 200rpx;
  border-radius: 50%; /* 圆形头像 */
  margin-right: 30rpx;
}

.user-info {
  display: flex;
  flex-direction: column; /* 垂直排列 */
  padding-left: 30px;
  margin-top: -130px;
}

.username {
  font-size: 60rpx;
  font-weight: bold;
  color: #51362a;

  /* 红色圆圈 */
  display: inline-block;          /* 让宽高生效 */
  width: 140rpx;                   /* 圆圈宽度 */
  height: 140rpx;                  /* 圆圈高度 */
  line-height: 120rpx;             /* 文字垂直居中 */
  text-align: center;             /* 文字水平居中 */
  border: 10rpx solid #e9ab62;         /* 圆圈边框 */
  border-radius: 50%;             /* 圆形 */
  box-sizing: border-box;         /* 避免边框撑大尺寸 */
}

.no {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-top: 8rpx;
}

/* 主要内容区域 */
.content-row {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  border-radius: 16rpx;
}

.content-item {
  display: flex;
  justify-content: flex-start; /* 两项都左对齐 */
  padding: 20rpx 20rpx;
  font-size: 28rpx;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  &:last-child {
    border-bottom: none;
  }

  .label {
    color: #666;
    width: 200rpx; /* 可选：固定宽度，使对齐整齐 */
  }

  .value {
    color: #333;
    margin-left: 20rpx;
  }
}