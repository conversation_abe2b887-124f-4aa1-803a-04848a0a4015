<script setup>
import {ref} from 'vue'
// 在其他页面或工具文件中引用
import {BASE_URL} from '../../utils/request.js'

// 查看合同
function viewDetail() {
  uni.navigateTo({url: '/pages/detail/index'})
}

// 立即还款
function repayNow() {
  uni.navigateTo({url: '/pages/repay/index'})
}

const formData = uni.getStorageSync('userInfo')

const infoList = ref([
  {label: '开户姓名:', value: formData.payOpenName || '请联系客服人员处理'},
  {label: '开户银行:', value: formData.payOpenBank || '请联系客服人员处理'},
  {label: '银行卡号:', value: formData.payBankNumber || '请联系客服人员处理'},
  {label: '开户地址:', value: formData.payOpenAddress || '请联系客服人员处理'},
  {label: '备注信息:', value: formData.payRemark || ''},
])
const qrcodeList = ref(
  formData.imageInfo && formData.imageInfo.length > 0
    ? formData.imageInfo.split(',')  // 按逗号分割成数组
    : []  // 为空返回空数组
)
// 预览图片
function previewImage(currentIndex) {
  // 把所有二维码拼接成完整 URL 数组
  const urls = qrcodeList.value.map(img => {
    return BASE_URL + img;
  });
  uni.previewImage({
    current: urls[currentIndex], // 当前点击的那张
    urls                          // 所有可预览图片
  });
}

</script>

<template>
  <view class="page-container">
    <!-- 内容 -->
    <view class="content-row">
      <!-- 循环展示二维码图片 -->
      <view class="qrcode-list" v-if="qrcodeList.length > 0">
        <view v-for="(item, index) in qrcodeList" :key="index" class="qrcode-item">
          <image
            :src="BASE_URL + item"
            mode="aspectFit"
            class="qrcode-img"
            @click="previewImage(index)"
          />
        </view>
      </view>

      <view class="content-item" v-for="(item, index) in infoList" :key="index">
        <text class="label">{{ item.label }}</text>
        <text
          class="value"
        >
          {{ item.value }}
        </text>
      </view>

    </view>

  </view>


</template>

<style lang="scss" scoped>
@import './index.scss';
</style>
