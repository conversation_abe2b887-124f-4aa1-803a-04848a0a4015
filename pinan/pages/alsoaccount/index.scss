.page-container {
  display: flex;
  flex-direction: column;
  min-height: 108vh;       /* 占满整个视口 */
}

/* 主要内容区域 */
.content-row {
  display: flex;
  flex-direction: column;
  margin: 40rpx 20rpx;
  padding: 40rpx 20rpx;
  border-radius: 20rpx;
  background-color: #fff;
}

.content-item {
  display: flex;
  justify-content: flex-start; /* 两项都左对齐 */
  padding: 20rpx 20rpx;
  font-size: 28rpx;

  &:last-child {
    border-bottom: none;
  }

  .label {
    color: #666;
    width: 200rpx; /* 可选：固定宽度，使对齐整齐 */
  }

  .value {
    color: #333;
    margin-left: 20rpx;
  }
}

.qrcode-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}

.qrcode-item {
  width: 300rpx;
  height: 300rpx;
}

.qrcode-img {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
  object-fit: contain; /* 对应 aspectFit */
}