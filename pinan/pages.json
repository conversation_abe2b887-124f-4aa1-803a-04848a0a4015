{
  "condition": {
    "current": 0,
    "list": [
      {
        "name": "登录",
        "path": "pages/login/index",
        "query": ""
      },
      {
        "name": "首页",
        "path": "pages/index/index",
        "query": ""
      },
      {
        "name": "借款",
        "path": "pages/borrow/borrow"
      },
      {
        "name": "还款",
        "path": "pages/also/also"
      },
      {
        "name": "我的",
        "path": "pages/my/my"
      },
      {
        "name": "隐私政策",
        "path": "pages/privacy/index"
      }
    ]
  },
  "pages": [
    {
      "path": "pages/login/index",
      "style": {
        "navigationBarBackgroundColor": "white",
        "navigationBarTitleText": "登陆"
      }
    },
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarBackgroundColor": "white",
        "navigationBarTitleText": "首页"
      }
    },
    {
      "path": "pages/privacy/index",
      "style": {
        "navigationBarBackgroundColor": "#f8f8f8",
        "navigationBarTitleText": "隐私政策"
      }
    },
    {
      "path": "pages/borrow/borrow",
      "style": {
        "navigationBarBackgroundColor": "white",
        "navigationBarTitleText": "借款",
        "navigationStyle": "default"
      }
    },
    {
      "path": "pages/also/also",
      "style": {
        "navigationBarBackgroundColor": "white",
        "navigationBarTitleText": "还款"
      }
    },
    {
      "path": "pages/alsoaccount/also",
      "style": {
        "navigationBarTitleText": "平台指定还款账户"
      }
    },
    {
      "path": "pages/contract/index",
      "style": {
        "navigationBarBackgroundColor": "#f8f8f8",
        "navigationBarTitleText": "合同"
      }
    },
    {
      "path": "pages/my/my",
      "style": {
        "navigationBarTitleText": "我的"
      }
    },
    {
      "path": "pages/userinfo/index",
      "style": {
        "navigationBarBackgroundColor": "white",
        "navigationBarTitleText": "个人信息"
      }
    },
    {
      "path": "pages/help/index",
      "style": {
        "navigationBarTitleText": "客户须知",
        "navigationBarBackgroundColor": "#f8f8f8"
      }
    }
  ],
  "globalStyle": {
    "navigationBarTitleText": "平安贷",
    "navigationBarTextStyle": "black",
    "navigationBarBackgroundColor": "#ffffff",
    "enablePullDownRefresh": false
  },
  "tabBar": {
    "borderStyle": "white",
    "backgroundColor": "#fff",
    "color": "#818181",
    "selectedColor": "#EF4F3F",
    "list": [
      {
        "text": "首页",
        "pagePath": "pages/index/index",
        "iconPath": "static/tab/nav-index.png",
        "selectedIconPath": "static/tab/nav-index-on.png"
      },
      {
        "text": "借款",
        "pagePath": "pages/borrow/borrow",
        "iconPath": "static/tab/nav-jk.png",
        "selectedIconPath": "static/tab/nav-jk-on.png"
      },
      {
        "text": "还款",
        "pagePath": "pages/also/also",
        "iconPath": "static/tab/nav-hk.png",
        "selectedIconPath": "static/tab/nav-hk-on.png"
      },
      {
        "text": "我的",
        "pagePath": "pages/my/my",
        "iconPath": "static/tab/nav-user.png",
        "selectedIconPath": "static/tab/nav-user-on.png"
      }
    ]
  },
  //  "subPackages": [{
  //    "root": "subpkg_task",
  //    "pages": [{
  //      "path": "detail/index",
  //      "style": {
  //        "navigationBarTitleText": "任务详情"
  //      }
  //    },
  //      {
  //        "path": "delay/index",
  //        "style": {
  //          "navigationBarTitleText": "延迟提货"
  //        }
  //      },
  //      {
  //        "path": "pickup/index",
  //        "style": {
  //          "navigationBarTitleText": "提货信息"
  //        }
  //      },
  //      {
  //        "path": "except/index",
  //        "style": {
  //          "navigationBarTitleText": "上报异常"
  //        }
  //      },
  //      {
  //        "path": "delivery/index",
  //        "style": {
  //          "navigationBarTitleText": "交货信息"
  //        }
  //      },
  //      {
  //        "path": "orders/index",
  //        "style": {
  //          "navigationBarTitleText": "查询商品"
  //        }
  //      },
  //      {
  //        "path": "guide/index",
  //        "style": {
  //          "navigationBarTitleText": "导航"
  //        }
  //      },
  //      {
  //        "path": "record/index",
  //        "style": {
  //          "navigationBarTitleText": "回车登记"
  //        }
  //      }
  //    ]
  //  },
  //    {
  //      "root": "subpkg_message",
  //      "pages": [{
  //        "path": "content/index",
  //        "style": {
  //          "navigationBarTitleText": "详情"
  //        }
  //      }]
  //    },
  //    {
  //      "root": "subpkg_user",
  //      "pages": [{
  //        "path": "truck/index",
  //        "style": {
  //          "navigationBarTitleText": "车辆信息"
  //        }
  //      },
  //        {
  //          "path": "task/index",
  //          "style": {
  //            "navigationBarTitleText": "任务数据"
  //          }
  //        },
  //        {
  //          "path": "settings/index",
  //          "style": {
  //            "navigationBarTitleText": "系统设置"
  //          }
  //        },
  //        {
  //          "path": "mobile/index",
  //          "style": {
  //            "navigationBarTitleText": "换绑手机"
  //          }
  //        },
  //        {
  //          "path": "password/index",
  //          "style": {
  //            "navigationBarTitleText": "修改密码"
  //          }
  //        },
  //        {
  //          "path": "notify/index",
  //          "style": {
  //            "navigationBarTitleText": "消息通知设置"
  //          }
  //        }
  //      ]
  //    }
  //  ],
  "uniIdRouter": {}
}