// utils/request.js

export  const BASE_URL = 'https://admin.phdwxl.com/api';
// export const BASE_URL = 'http://localhost:8088';

class Request {
  constructor() {
    this.interceptor = {
      request: null,
      response: null,
    };
  }

  setRequestInterceptor(fn) {
    this.interceptor.request = fn;
  }

  setResponseInterceptor(fn) {
    this.interceptor.response = fn;
  }

  request(options) {
    return new Promise((resolve, reject) => {
      // 默认处理请求参数
      let {url, method = 'GET', data = {}, header = {}, noAuth = false} = options;

      // 请求拦截器处理
      if (this.interceptor.request) {
        const newOptions = this.interceptor.request({url, method, data, header, noAuth});
        url = newOptions.url || url;
        method = newOptions.method || method;
        data = newOptions.data || data;
        header = newOptions.header || header;
        noAuth = newOptions.noAuth !== undefined ? newOptions.noAuth : noAuth;
      }

      // 非登录请求且需要鉴权，自动带token
      if (!noAuth) {
        const token = uni.getStorageSync('token');
        if (!token) {
          uni.showToast({title: '请先登录', icon: 'none'});
          uni.navigateTo({url: '/pages/login/index'});
          reject('未登录');
          return;
        }
        header.Authorization = `Bearer ${token}`;
      }

      uni.request({
        url: BASE_URL + url,
        method,
        data,
        header: {
          'Content-Type': 'application/json',
          ...header,
        },
        success: (res) => {
          let {statusCode, data} = res;

          // 响应拦截器处理
          if (this.interceptor.response) {
            const resResult = this.interceptor.response(data, statusCode);
            if (resResult === false) {
              // 拦截器返回 false，阻止继续执行
              reject(data);
              return;
            }
            // 如果拦截器返回新的 data，可以赋值覆盖
            if (resResult !== undefined) {
              data = resResult;
            }
          }

          if (statusCode === 200) {
            resolve(data);
          } else if (statusCode === 401) {
            uni.showToast({title: '登录已过期，请重新登录', icon: 'none'});
            uni.navigateTo({url: '/pages/login/index'});
            reject(data);
          } else {
            uni.showToast({title: data.message || '请求失败', icon: 'none'});
            reject(data);
          }
        },
        fail: (err) => {
          uni.showToast({title: '网络异常', icon: 'none'});
          reject(err);
        }
      });
    });
  }

  get(url, data = {}, options = {}) {
    return this.request({url, method: 'GET', data, ...options});
  }

  post(url, data = {}, options = {}) {
    return this.request({url, method: 'POST', data, ...options});
  }
}

const requestInstance = new Request();

// 设置请求拦截器示例
requestInstance.setRequestInterceptor((options) => {
  return options; // 必须返回，否则请求参数无法继续传递
});

// 设置响应拦截器示例
requestInstance.setResponseInterceptor((data, statusCode) => {
  // 统一判断接口返回的业务错误码（假设data.code是业务码）
  if (data && data.code && data.code === 401) {
    uni.navigateTo({url: '/pages/login/index'})
    return false;
  }
  // 统一处理业务错误
  if (data && data.code && data.code !== 200) {
    // 判断 msg 内容
    let toastMsg = data.msg || '网络异常...';
    if (toastMsg.includes('用户不存在')) {
      toastMsg = '手机号错误';
    }
    uni.showToast({ title: toastMsg, icon: 'none' });
    // 返回 false 阻止 Promise 继续 resolve
    return false;
  }

  return data;
});

export default requestInstance;
