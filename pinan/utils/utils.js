export const utils = {
	/**
	 * 用户反馈（轻提示）
	 * @param {string} title 提示文字内容
	 * @param {string} icon 提示图标类型
	 */
	toast(title = '数据加载失败！', icon = 'none') {
		uni.showToast({
			title,
			icon,
			mask: true,
		})
	}
}

// 方便全局进行引用
uni.utils = utils

/**
 * 数字转中文大写金额（整数部分）
 * @param {number|string} num - 要转换的数字
 * @returns {string} 中文大写金额
 */
export function numberToChinese(num) {
	if (typeof num !== 'number') {
		num = parseFloat(num)
	}
	if (isNaN(num)) return ''

	const units = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿']
	const chars = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']

	let str = ''
	let unitPos = 0
	let zero = true

	while (num > 0) {
		const section = num % 10
		if (section === 0) {
			if (!zero) {
				zero = true
				str = chars[0] + str
			}
		} else {
			zero = false
			str = chars[section] + units[unitPos] + str
		}
		unitPos++
		num = Math.floor(num / 10)
	}

	// 处理重复零
	str = str.replace(/零+/g, '零')
	// 去掉末尾零
	str = str.replace(/零$/, '')

	return str
}

// utils/date.js
export function get24thDate(inputDate) {
	// 如果没传参数就用当前时间
	const date = inputDate ? new Date(inputDate) : new Date()

	const year = date.getFullYear()
	let month = date.getMonth() // 0-11
	const day = date.getDate()

	let targetYear = year
	let targetMonth = month

	if (day < 24) {
		// 本月 24 号
		targetMonth = month
	} else {
		// 下个月 24 号
		targetMonth = month + 1
		if (targetMonth > 11) {
			targetMonth = 0
			targetYear++
		}
	}
	const targetDate = new Date(targetYear, targetMonth, 24)

	// 格式化
	const yyyy = targetDate.getFullYear()
	const mm = String(targetDate.getMonth() + 1).padStart(2, '0')
	const dd = String(targetDate.getDate()).padStart(2, '0')
	return `${yyyy}-${mm}-${dd}`
}
