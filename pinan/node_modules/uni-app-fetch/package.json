{"name": "uni-app-fetch", "version": "0.0.3", "description": "A network request library for uni-app.", "main": "dist/index.js", "scripts": {"build": "tsc", "publish": "npm publish", "prepare": "husky install"}, "publishConfig": {"access": "public"}, "keywords": ["uni-app", "miniprogram", "http", "fetch"], "types": "dist/index.d.ts", "author": "lotjol <<EMAIL>>", "license": "ISC", "repository": "lotjol/uni-app-fetch", "homepage": "https://github.com/lotjol/uni-app-fetch#readme", "devDependencies": {"@dcloudio/types": "^3.3.3", "cz-conventional-changelog": "^3.3.0", "husky": "^8.0.0", "typescript": "^5.0.4"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}