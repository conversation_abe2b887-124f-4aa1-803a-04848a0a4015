{"name": "deep-pick-omit", "type": "module", "version": "1.2.1", "packageManager": "pnpm@9.12.0", "description": "Deep-pick and deep-omit objects with typesafe paths.", "author": "PraZ", "license": "MIT", "repository": "github:prazdevs/deep-pick-omit", "sideEffects": false, "exports": {".": {"types": {"import": "./dist/index.d.mts", "require": "./dist/index.d.cts"}, "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "unbuild", "lint": "eslint .", "prepublishOnly": "pnpm build", "release": "bumpp", "test": "vitest --typecheck", "test:coverage": "vitest run --coverage", "test:ui": "vitest --typecheck --ui", "typecheck": "tsc --noEmit", "prepare": "simple-git-hooks"}, "devDependencies": {"@antfu/eslint-config": "^3.7.3", "@vitest/coverage-v8": "^2.1.2", "@vitest/ui": "^2.1.2", "bumpp": "^9.6.1", "eslint": "^9.11.1", "lint-staged": "^15.2.10", "simple-git-hooks": "^2.11.1", "typescript": "^5.6.2", "unbuild": "^2.0.0", "vite": "^5.4.8", "vitest": "^2.1.2"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}