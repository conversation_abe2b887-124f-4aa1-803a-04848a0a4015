{"name": "pinia-plugin-persistedstate", "type": "module", "version": "4.4.1", "packageManager": "pnpm@10.12.4", "description": "Configurable persistence and rehydration of Pinia stores.", "author": "<PERSON><PERSON> <<EMAIL>> (https://praz.dev)", "license": "MIT", "homepage": "https://prazdevs.github.io/pinia-plugin-persistedstate", "repository": {"type": "git", "url": "git+https://github.com/prazdevs/pinia-plugin-persistedstate.git"}, "bugs": {"url": "https://github.com/prazdevs/pinia-plugin-persistedstate/issues"}, "keywords": ["vue", "store", "pinia", "persistence", "pinia-plugin", "nuxt", "nuxt-module"], "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./nuxt": {"import": {"types": "./dist/nuxt/types.d.mts", "default": "./dist/nuxt/module.mjs"}}}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "sponsor": {"url": "https://github.com/sponsors/prazdevs"}, "scripts": {"build": "pnpm build:plugin && pnpm build:umd && pnpm build:module", "build:plugin": "tsup src/index.ts --dts --format esm,cjs", "build:module": "nuxt-module-build build --outDir dist/nuxt", "build:umd": "rollup -c rollup.config.js", "dev": "nuxi dev playground", "dev:build": "nuxi build playground", "dev:prepare": "nuxt-module-build build --stub && nuxt-module-build prepare && nuxi prepare playground", "release": "changelogen --release --push", "docs": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs", "lint": "eslint .", "lint:fix": "eslint . --fix", "publint": "publint", "typecheck": "nuxi typecheck"}, "peerDependencies": {"@nuxt/kit": ">=3.0.0", "@pinia/nuxt": ">=0.10.0", "pinia": ">=3.0.0"}, "peerDependenciesMeta": {"@nuxt/kit": {"optional": true}, "@pinia/nuxt": {"optional": true}, "pinia": {"optional": true}}, "dependencies": {"deep-pick-omit": "^1.2.1", "defu": "^6.1.4", "destr": "^2.0.5"}, "devDependencies": {"@antfu/eslint-config": "^4.16.1", "@iconify-json/catppuccin": "^1.2.11", "@nuxt/devtools": "^2.5.0", "@nuxt/kit": "^3.17.5", "@nuxt/module-builder": "^1.0.1", "@nuxt/schema": "^3.17.5", "@pinia/nuxt": "^0.11.1", "@rollup/plugin-node-resolve": "^16.0.1", "@shikijs/vitepress-twoslash": "^3.7.0", "@types/node": "^24.0.7", "changelogen": "^0.6.1", "esbuild": "^0.25.5", "eslint": "^9.30.0", "eslint-plugin-format": "^1.0.1", "lint-staged": "^16.1.2", "nuxt": "^3.17.5", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "link:", "publint": "^0.3.12", "rollup": "^4.44.1", "rollup-plugin-esbuild": "^6.2.1", "simple-git-hooks": "^2.13.0", "tsup": "^8.5.0", "typescript": "~5.8.3", "vitepress": "^1.6.3", "vitepress-plugin-group-icons": "^1.6.0", "vue": "^3.5.17", "vue-tsc": "^2.2.10"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}