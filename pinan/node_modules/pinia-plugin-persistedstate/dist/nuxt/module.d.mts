import * as _nuxt_schema from '@nuxt/schema';
import { CookiesStorageOptions } from '../dist/nuxt/runtime/storages.js';
import { Path } from 'deep-pick-omit';
import { StateTree, PiniaPluginContext } from 'pinia';

/**
 * Synchronous storage based on Web Storage API.
 * @see https://developer.mozilla.org/en-US/docs/Web/API/Storage
 */
interface StorageLike {
    /**
     * Get a key's value if it exists.
     */
    getItem: (key: string) => string | null;
    /**
     * Set a key with a value, or update it if it exists.
     */
    setItem: (key: string, value: string) => void;
}
/**
 * Serializer implementation to stringify/parse state.
 */
interface Serializer {
    /**
     * Serialize state into string before storing.
     * @default JSON.stringify
     * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify
     */
    serialize: (data: StateTree) => string;
    /**
     * Deserializes string into state before hydrating.
     * @default destr
     * @see https://github.com/unjs/destr
     */
    deserialize: (data: string) => StateTree;
}
interface Persistence<State extends StateTree = StateTree> {
    /**
     * Storage key to use.
     * @default $store.id
     */
    key: string;
    /**
     * Log errors in console.
     * @default false
     */
    debug: boolean;
    /**
     * Synchronous storage to persist the state.
     */
    storage: StorageLike;
    /**
     * Serializer to serialize/deserialize state into storage.
     */
    serializer: Serializer;
    /**
     * Hook called before hydrating store.
     */
    beforeHydrate?: (context: PiniaPluginContext) => void;
    /**
     * Hook called after hydrating store.
     */
    afterHydrate?: (context: PiniaPluginContext) => void;
    /**
     * Dot-notation paths to pick from state before persisting.
     */
    pick?: Path<State>[] | string[];
    /**
     * Dot-notation paths to omit from state before persisting.
     */
    omit?: Path<State>[] | string[];
}
type PersistenceOptions<State extends StateTree = StateTree> = Partial<Persistence<State>>;
declare module 'pinia' {
    interface DefineStoreOptionsBase<S extends StateTree, Store> {
        /**
         * Persist store in storage
         * @see https://prazdevs.github.io/pinia-plugin-persistedstate
         */
        persist?: boolean | PersistenceOptions<S> | PersistenceOptions<S>[];
    }
    interface PiniaCustomProperties {
        /**
         * Hydrate store from configured storage
         * Warning: this is for advances usecases, make sure you know what you're doing
         */
        $hydrate: (opts?: {
            runHooks?: boolean;
        }) => void;
        /**
         * Persist store into configured storage
         * Warning: this is for advances usecases, make sure you know what you're doing
         */
        $persist: () => void;
    }
}

type ModuleOptions = Pick<PersistenceOptions, 'debug'> & {
    /**
     * Default storage for persistence. Only accepts presets.
     */
    storage?: 'cookies' | 'localStorage' | 'sessionStorage';
    /**
     * Global key template, allow pre/postfixing store keys.
     * @example 'my-%id-persistence' will yield 'my-<store-id>-persistence'
     */
    key?: `${string}%id${string}`;
    /**
     * Options used globally by default cookie storage.
     * Ignored for other storages.
     */
    cookieOptions?: Omit<CookiesStorageOptions, 'encode' | 'decode'>;
    /**
     * Automatically persist all stores with global defaults, opt-out individually.
     */
    auto?: boolean;
};
declare const _default: _nuxt_schema.NuxtModule<ModuleOptions, ModuleOptions, false>;

declare module '@nuxt/schema' {
    interface PublicRuntimeConfig {
        piniaPluginPersistedstate: ModuleOptions;
    }
}

export { _default as default };
