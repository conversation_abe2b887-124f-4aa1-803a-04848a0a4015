import { createJiti } from "../node_modules/.pnpm/jiti@2.4.2/node_modules/jiti/lib/jiti.mjs";

const jiti = createJiti(import.meta.url, {
  "interopDefault": true,
  "alias": {
    "pinia-plugin-persistedstate": "/home/<USER>/work/pinia-plugin-persistedstate/pinia-plugin-persistedstate"
  },
  "transformOptions": {
    "babel": {
      "plugins": []
    }
  }
})

/** @type {import("/home/<USER>/work/pinia-plugin-persistedstate/pinia-plugin-persistedstate/src/module.js")} */
const _module = await jiti.import("/home/<USER>/work/pinia-plugin-persistedstate/pinia-plugin-persistedstate/src/module.ts");

export default _module?.default ?? _module;