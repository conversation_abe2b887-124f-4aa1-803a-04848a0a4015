if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((n=>t.resolve(e()).then((()=>n))),(n=>t.resolve(e()).then((()=>{throw n}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.BigInt64Array,BigUint64Array=e.BigUint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";function t(e,t,...n){uni.__log__?uni.__log__(e,t,...n):console[e].apply(console,[...n,t])}const n=new class{constructor(){this.interceptor={request:null,response:null}}setRequestInterceptor(e){this.interceptor.request=e}setResponseInterceptor(e){this.interceptor.response=e}request(e){return new Promise(((t,n)=>{let{url:a,method:l="GET",data:r={},header:o={},noAuth:s=!1}=e;if(this.interceptor.request){const e=this.interceptor.request({url:a,method:l,data:r,header:o,noAuth:s});a=e.url||a,l=e.method||l,r=e.data||r,o=e.header||o,s=void 0!==e.noAuth?e.noAuth:s}if(!s){const e=uni.getStorageSync("token");if(!e)return uni.showToast({title:"请先登录",icon:"none"}),uni.navigateTo({url:"/pages/login/index"}),void n("未登录");o.Authorization=`Bearer ${e}`}uni.request({url:"https://admin.pinanhua.com/api"+a,method:l,data:r,header:{"Content-Type":"application/json",...o},success:e=>{let{statusCode:a,data:l}=e;if(this.interceptor.response){const e=this.interceptor.response(l,a);if(!1===e)return void n(l);void 0!==e&&(l=e)}200===a?t(l):401===a?(uni.showToast({title:"登录已过期，请重新登录",icon:"none"}),uni.navigateTo({url:"/pages/login/index"}),n(l)):(uni.showToast({title:l.message||"请求失败",icon:"none"}),n(l))},fail:e=>{uni.showToast({title:"网络异常",icon:"none"}),n(e)}})}))}get(e,t={},n={}){return this.request({url:e,method:"GET",data:t,...n})}post(e,t={},n={}){return this.request({url:e,method:"POST",data:t,...n})}};n.setRequestInterceptor((e=>(t("log","at utils/request.js:106","请求拦截器：",e),e))),n.setResponseInterceptor(((e,n)=>(t("log","at utils/request.js:113",401===e.code),e&&e.code&&401===e.code?(uni.navigateTo({url:"/pages/login/index"}),!1):e&&e.code&&200!==e.code?(uni.showToast({title:e.message||"业务错误",icon:"none"}),!1):e)));const a=(e,t)=>{const n=e.__vccOpts||e;for(const[a,l]of t)n[a]=l;return n},l=a({__name:"index",setup(a){const l=e.ref(!1),r=e.ref("");function o(e){l.value=e.detail.value.length>0,t("log","at pages/login/index.vue:15","checkbox change",e.detail.value),t("log","at pages/login/index.vue:16",l.value)}function s(){uni.navigateTo({url:"/pages/privacy/index"})}function c(){var e;r.value?l.value?(e=r.value,/^1[3-9]\d{9}$/.test(e)?n.post("/h5Login",{username:r.value,password:r.value},{noAuth:!0}).then((e=>{uni.setStorageSync("token",e.data),n.get("/h5GetInfo",{}).then((e=>{uni.setStorageSync("userInfo",e.data),uni.switchTab({url:"/pages/index/index"}),t("log","at pages/login/index.vue:54","登录成功",e)})).catch((e=>{}))})).catch((e=>{t("error","at pages/login/index.vue:62","登录失败",e)})):uni.showToast({title:"手机号格式不正确",icon:"none"})):uni.showToast({title:"请先同意隐私政策",icon:"none"}):uni.showToast({title:"请输入手机号",icon:"none"})}return(t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"page-container"},[e.createElementVNode("view",{class:"user-profile"}),e.createElementVNode("view",{class:"user-login"},[e.createElementVNode("view",{class:"login-type"},[e.createElementVNode("view",{class:"title"},"平安贷")]),e.createElementVNode("view",{class:"login-content"},[e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":n[0]||(n[0]=e=>r.value=e),type:"text",placeholder:"请输入手机号码",class:"uni-input-input"},null,512),[[e.vModelText,r.value]]),e.createElementVNode("view",{class:"checkbox-wrapper"},[e.createElementVNode("checkbox-group",{onChange:o},[e.createElementVNode("label",{class:"checkbox-label"},[e.createElementVNode("checkbox",{checked:l.value,color:"#f98a1d",style:{transform:"scale(0.8)"}},null,8,["checked"]),e.createElementVNode("text",null,"我已阅读并同意"),e.createElementVNode("text",{class:"privacy-link",onClick:s},"《隐私政策》")])],32)]),e.createElementVNode("button",{class:"submit-button",onClick:c},"登 录")])])]))}},[["__scopeId","data-v-be861571"]]),r=a({__name:"index",setup(t){const n=e.ref([{title:"借款",img:"/static/nav/nav-1.png",url:"/pages/borrow/borrow"},{title:"还款",img:"/static/nav/nav-2.png",url:"/pages/also/also"},{title:"隐私协议",img:"/static/nav/nav-3.png",url:"/pages/privacy/index"},{title:"帮助",img:"/static/nav/nav-4.png",url:"/pages/help/index"}]);function a(){uni.switchTab({url:"/pages/also/also"})}function l(){uni.navigateTo({url:"/pages/alsoaccount/also"})}function r(){uni.switchTab({url:"/pages/borrow/borrow"})}const o=e.ref(["欢迎来到平安贷","最新活动：限时优惠","请注意保护个人信息"]),s=uni.getStorageSync("userInfo"),c=Math.ceil(((new Date).getTime()-new Date(s.endTime))/864e5);return(t,i)=>(e.openBlock(),e.createElementBlock("view",{class:"page-container"},[e.createElementVNode("swiper",{autoplay:"",circular:"","indicator-dots":"",class:"banner"},[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList([1,2,3,4],((t,n)=>e.createElementVNode("swiper-item",{key:n},[e.createElementVNode("image",{src:`/static/banner/index-${t}.png`,mode:"aspectFill",class:"banner-img"},null,8,["src"])]))),64))]),e.createElementVNode("view",{class:"notice-bar"},[e.createElementVNode("image",{src:"/static/horn.png",class:"horn-icon"}),e.createElementVNode("swiper",{autoplay:"",vertical:"",circular:"",interval:"2000",class:"notice-swiper"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.value,((t,n)=>(e.openBlock(),e.createElementBlock("swiper-item",{key:n},[e.createElementVNode("text",{class:"notice-text"},"📣"),e.createElementVNode("text",{class:"notice-text"},e.toDisplayString(t),1)])))),128))])]),e.createElementVNode("view",{class:"icon-row"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"icon-item",key:n,onClick:e=>{var n;"/pages/borrow/borrow"!==(n=t.url)&&"/pages/also/also"!==n?uni.navigateTo({url:n}):uni.switchTab({url:n})}},[e.createElementVNode("view",{class:"icon-circle"},[e.createElementVNode("image",{src:t.img,mode:"aspectFit",class:"icon-img"},null,8,["src"])]),e.createElementVNode("text",null,e.toDisplayString(t.title),1)],8,["onClick"])))),128))]),e.createElementVNode("view",{class:"dh-text"}," 待还款项 "),e.createElementVNode("view",{class:"readonly"},[e.createElementVNode("view",{class:"readonly-form"},[e.createElementVNode("view",{class:e.normalizeClass(["status-watermark",e.unref(c)>0?"overdue":"normal"])},e.toDisplayString(e.unref(c)>0?"已逾期":"正常"),3),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"贷款金额"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(s).loanAmount),1)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"到期日期"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(s).endTime),1)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"逾期天数"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(c)<0?"未逾期":e.unref(c)+"天"),1)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"逾期金额"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(s).expectAmount),1)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"应还金额"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(s).payableAmount),1)])]),e.createElementVNode("view",{class:"action-buttons"},[e.createElementVNode("button",{class:"btn btn-detail",onClick:a},"查看详情"),e.createElementVNode("button",{class:"btn btn-repay",onClick:l},"立即还款")])]),e.createElementVNode("view",{class:"dh-text"}," 我要借款 "),e.createElementVNode("view",{class:"readonly"},[e.createElementVNode("view",{class:"readonly-form"},[e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"最多可借"),e.createElementVNode("text",{class:"value"},e.toDisplayString(2e5))]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"利率低至"),e.createElementVNode("text",{class:"value"},e.toDisplayString(.01)+"%")]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"最长期限"),e.createElementVNode("text",{class:"value"},e.toDisplayString(365)+" 天")])]),e.createElementVNode("view",{class:"action-buttons"},[e.createElementVNode("button",{class:"btn btn-detail",onClick:r},"查看详情"),e.createElementVNode("button",{class:"btn btn-repayjK",onClick:r},"立即借款")])])]))}},[["__scopeId","data-v-854712b6"]]);const o=a({},[["render",function(t,n){return e.openBlock(),e.createElementBlock("view",{class:"privacy-page"},[e.createElementVNode("scroll-view",{"scroll-y":"",class:"privacy-content"},[e.createElementVNode("view",{class:"section"},[e.createElementVNode("text",{class:"section-title"},"隐私政策"),e.createElementVNode("text",null," 您的隐私对我们至关重要。本政策解释了我们会收集哪些信息、如何使用这些信息以及您对数据的控制权。请仔细阅读。 ")]),e.createElementVNode("view",{class:"section"},[e.createElementVNode("text",{class:"section-title"},"1. 我们收集的信息"),e.createElementVNode("view",{class:"subsection"},[e.createElementVNode("text",{class:"subsection-title"},"1.1 您直接提供的信息"),e.createElementVNode("text",null,"• 账户注册：姓名、邮箱、手机号等"),e.createElementVNode("text",null,"• 服务交互：订单记录、反馈内容、联系方式等")]),e.createElementVNode("view",{class:"subsection"},[e.createElementVNode("text",{class:"subsection-title"},"1.2 自动收集的信息"),e.createElementVNode("text",null,"• 设备信息：设备型号、操作系统、唯一设备标识符（如IMEI）"),e.createElementVNode("text",null,"• 使用数据：访问时间、功能使用频率、崩溃日志等"),e.createElementVNode("text",null,"• 位置信息（仅限相关服务）：GPS或IP地址定位（需用户授权）")]),e.createElementVNode("view",{class:"subsection"},[e.createElementVNode("text",{class:"subsection-title"},"1.3 第三方来源的信息"),e.createElementVNode("text",null,"• 通过社交媒体账号登录时，可能获取公开资料（如头像、昵称）")])]),e.createElementVNode("view",{class:"section"},[e.createElementVNode("text",{class:"section-title"},"2. 我们如何使用信息"),e.createElementVNode("text",null,"• 提供和优化服务（如账户管理、客服支持）"),e.createElementVNode("text",null,"• 安全保障：检测异常活动、防止欺诈"),e.createElementVNode("text",null,"• 统计分析：改进产品功能（匿名化处理数据）"),e.createElementVNode("text",null,"• 营销推广（需用户单独同意）：发送促销信息")]),e.createElementVNode("view",{class:"section"},[e.createElementVNode("text",{class:"section-title"},"3. 数据共享与披露"),e.createElementVNode("text",null,"• 第三方服务商：支付处理、云存储等（需签署保密协议）"),e.createElementVNode("text",null,"• 法律要求：响应法院指令或政府调查"),e.createElementVNode("text",null,"• 商业转让：如发生并购，用户数据可能转移")]),e.createElementVNode("view",{class:"section"},[e.createElementVNode("text",{class:"section-title"},"4. 数据存储与安全"),e.createElementVNode("text",null,"• 存储期限：满足法律要求或业务所需的最短时间"),e.createElementVNode("text",null,"• 安全措施：加密传输、访问控制、定期审计")]),e.createElementVNode("view",{class:"section"},[e.createElementVNode("text",{class:"section-title"},"5. 您的权利"),e.createElementVNode("text",null,"• 访问、更正或删除个人信息"),e.createElementVNode("text",null,"• 撤回同意（部分功能可能受限）"),e.createElementVNode("text",null,"• 注销账户（通过设置或联系客服）")]),e.createElementVNode("view",{class:"section"},[e.createElementVNode("text",{class:"section-title"},"6. 儿童隐私"),e.createElementVNode("text",null,"• 服务不面向13周岁（或所在国法定年龄）以下用户"),e.createElementVNode("text",null,"• 如意外收集儿童数据，将立即删除")]),e.createElementVNode("view",{class:"section"},[e.createElementVNode("text",{class:"section-title"},"7. 政策更新"),e.createElementVNode("text",null,"• 修改政策会通过App内公告或邮件通知"),e.createElementVNode("text",null,"• 继续使用视为接受更新条款")])])])}],["__scopeId","data-v-1708030c"]]),s=a({__name:"borrow",setup(t){const n=e.ref([{titleLine1:"200000",titleLine2:"最高可借",img:"/static/nav/borrow-1.png"},{titleLine1:"0.25%",titleLine2:"利率低至",img:"/static/nav/borrow-2.png"},{titleLine1:"期限最长",titleLine2:"最长可至24个月",img:"/static/nav/nav-3.png"},{titleLine1:"灵活还款",titleLine2:"支付宝/微信/银行卡",img:"/static/nav/borrow-4.png"}]);function a(){uni.showToast({title:"逾期暂不开放",icon:"none"})}return(t,l)=>(e.openBlock(),e.createElementBlock("view",{class:"page-container"},[e.createElementVNode("view",{class:"banner"},[e.createElementVNode("image",{src:"/static/banner/borrow.png",mode:"aspectFill",class:"banner-img"}),e.createElementVNode("view",{class:"banner-text"},[e.createElementVNode("view",null,"最高可借额度"),e.createElementVNode("view",null,"200000")])]),e.createElementVNode("view",{class:"icon-row"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"icon-item",key:n},[e.createElementVNode("view",{class:"icon-circle"},[e.createElementVNode("image",{src:t.img,mode:"aspectFit",class:"icon-img"},null,8,["src"])]),e.createElementVNode("view",{class:"icon-text"},[e.createElementVNode("text",{class:"title-line1"},e.toDisplayString(t.titleLine1),1),e.createElementVNode("text",null,e.toDisplayString(t.titleLine2),1)])])))),128))]),e.createElementVNode("view",{class:"action-buttons"},[e.createElementVNode("button",{class:"btn btn-repay",onClick:a},"立即借款")])]))}},[["__scopeId","data-v-caa4b788"]]),c=a({__name:"also",setup(t){function n(){uni.navigateTo({url:"/pages/contract/index"})}function a(){uni.navigateTo({url:"/pages/alsoaccount/also"})}const l=uni.getStorageSync("userInfo"),r=Math.ceil(((new Date).getTime()-new Date(l.endTime))/864e5);return(t,o)=>(e.openBlock(),e.createElementBlock("view",{class:"page-container"},[e.createElementVNode("view",{class:"banner"},[e.createElementVNode("image",{src:"/static/banner/also.png",mode:"aspectFill",class:"banner-img"}),e.createElementVNode("view",{class:"banner-text"},[e.createElementVNode("view",null,"应还金额"),e.createElementVNode("view",null,e.toDisplayString(e.unref(l).payableAmount),1)])]),e.createElementVNode("view",{class:"content-row"},[e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"借款人姓名"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(l).userName),1)]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"身份证号"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(l).idCard),1)]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"手机号码"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(l).phoneNumber),1)]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"开户银行"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(l).openBank),1)]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"下款卡号"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(l).bankNumber),1)]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"下款时间"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(l).startTime),1)]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"到期时间"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(l).endTime),1)]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"逾期天数"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(r)<0?"未逾期":e.unref(r)+"天"),1)]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"贷款金额"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(l).loanAmount),1)]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"逾期金额"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(l).expectAmount),1)]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"应还金额"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(l).payableAmount),1)]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"欠款状态"),e.createElementVNode("text",{class:e.normalizeClass(["value",{"status-overdue":e.unref(r)>0,"status-normal":e.unref(r)<=0}])},e.toDisplayString(e.unref(r)<0?"未逾期":"已逾期"),3)])]),e.createElementVNode("view",{class:"action-buttons"},[e.createElementVNode("button",{class:"btn btn-detail",onClick:n},"查看合同"),e.createElementVNode("button",{class:"btn btn-repay",onClick:a},"立即还款")])]))}},[["__scopeId","data-v-6b194581"]]),i=a({__name:"also",setup(t){const n=e.ref([{label:"开户姓名:",value:"请联系客服人员处理"},{label:"开户银行:",value:"请联系客服人员处理"},{label:"银行卡号:",value:"请联系客服人员处理"}]);return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"page-container"},[e.createElementVNode("view",{class:"content-row"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"content-item",key:n},[e.createElementVNode("text",{class:"label"},e.toDisplayString(t.label),1),e.createElementVNode("text",{class:"value"},e.toDisplayString(t.value),1)])))),128))])]))}},[["__scopeId","data-v-00e30821"]]),u={toast(e="数据加载失败！",t="none"){uni.showToast({title:e,icon:t,mask:!0})}};function m(e){if("number"!=typeof e&&(e=parseFloat(e)),isNaN(e))return"";const t=["","拾","佰","仟","万","拾","佰","仟","亿"],n=["零","壹","贰","叁","肆","伍","陆","柒","捌","玖"];let a="",l=0,r=!0;for(;e>0;){const o=e%10;0===o?r||(r=!0,a=n[0]+a):(r=!1,a=n[o]+t[l]+a),l++,e=Math.floor(e/10)}return a=a.replace(/零+/g,"零"),a=a.replace(/零$/,""),a}function d(e){const t=e?new Date(e):new Date,n=t.getFullYear();let a=t.getMonth();let l=n,r=a;t.getDate()<24?r=a:(r=a+1,r>11&&(r=0,l++));const o=new Date(l,r,24);return`${o.getFullYear()}-${String(o.getMonth()+1).padStart(2,"0")}-${String(o.getDate()).padStart(2,"0")}`}uni.utils=u;const p=a({__name:"index",setup(t){const n=uni.getStorageSync("userInfo");return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"contract-container"},[e.createElementVNode("view",{class:"contract-title"},"贷款合同"),e.createElementVNode("view",{class:"contract-body"},[e.createElementVNode("text",null,"甲(出借人):平安贷有限公司"),e.createElementVNode("text",null,"乙(借款人):"+e.toDisplayString(e.unref(n).userName),1),e.createElementVNode("text",null,"身份证:"+e.toDisplayString(e.unref(n).idCard),1),e.createElementVNode("text",null,"手机号:"+e.toDisplayString(e.unref(n).phoneNumber),1),e.createElementVNode("text",null," 甲乙双方本着平等自愿、诚实守信的原则,经协商一致,达成本小额贷款合同,并保证共同遵守执行。 "),e.createElementVNode("text",{class:"subtitle"},"一、借款金额"),e.createElementVNode("text",{class:"paragraph"}," 1. 乙方向甲方借款人(大写:人民币"+e.toDisplayString(e.unref(m)(e.unref(n).loanAmount))+"元整元)(小写:"+e.toDisplayString(e.unref(n).loanAmount)+" 元)。 ",1),e.createElementVNode("text",{class:"subtitle"},"二、借款利息"),e.createElementVNode("text",{class:"paragraph"},"1. 借款日利率0.0025。"),e.createElementVNode("text",{class:"subtitle"},"三、借款期限"),e.createElementVNode("text",{class:"paragraph"}," 1. 借款期限为,从"+e.toDisplayString(e.unref(n).startTime)+"起至 "+e.toDisplayString(e.unref(d)(e.unref(n).startTime))+"为第一 个月还款日。如实际放款日与该日期不符,以实际 借款日为准。乙方收到借款后应当出具收据,甲方 所出具的借据为本小额贷款合同附件,与本小额贷 款合同具有同等法律效力。 ",1),e.createElementVNode("text",{class:"subtitle"},"四、保证条款:"),e.createElementVNode("text",{class:"paragraph"}," 1. 借款方不得用借款进行违法活动。否则,甲方有权 要求乙方立即还本付息,所产生的法律后果由乙方 自负。 "),e.createElementVNode("text",{class:"paragraph"}," 2. 借款方必须按合同规定的定期还本付息。逾期不还 的部分,借款方有权限追回借款并收取每天借款总 金额的0.25%逾期费用。 "),e.createElementVNode("text",{class:"subtitle"},"五、甲方以转账的方式将借款项打入乙方账户。"),e.createElementVNode("text",{class:"subtitle"},"六、违约责任"),e.createElementVNode("text",{class:"paragraph"}," 1. 乙方如未按合同规定还款,乙方应当承担违约金以 及因诉讼发生的律师、诉讼费、差旅费等费用。 "),e.createElementVNode("text",{class:"paragraph"}," 2. 当甲方认为借款人发生或可能发生影响偿还能力之 情形时,甲方有权提前收回借款,借款人应及时返 还,借款人及保证人不得以任何理由抗辩。 "),e.createElementVNode("text",{class:"subtitle"},"七、合同争议的解决方式"),e.createElementVNode("text",{class:"paragraph"}," 本合同在履行过程中发生的争议,由当事人双方友好 协商解决,也可以由第三人调解。协商或调解不成 的,可依法向甲方所在地人民法院提起诉讼。 "),e.createElementVNode("text",{class:"paragraph"}," 1. 乙方支付服务费后借款合同立即生效,如乙方放弃 借款或是乙方造成的过错,导致下款失败,乙方自 己承担与甲方无关(包含服务费)乙方需按时还 款。 "),e.createElementVNode("text",{class:"paragraph"}," 2. 如乙方问题需要修改收款信息或解冻账户需收取一 定的押金费用,根据通过的额度百分比计算收取。 "),e.createElementVNode("text",{class:"paragraph"}," 3. 提现之前乙方需支付(服务费=审批额度*0.05)待 乙方还清2期借款后退款到乙方个人账户钱包。 "),e.createElementVNode("text",{class:"paragraph"}," 4. 本小额贷款合同自各方签字(含电子签名)之日起 生效。合同文本具有同等法律效力。 "),e.createElementVNode("text",{class:"subtitle"},"八、甲方责任"),e.createElementVNode("text",{class:"paragraph"}," 1. 如乙方已支付服务费后,甲方问题超过5分钟不放 款,乙方支付的5%服务费无条件全额原路退还。 ")]),e.createElementVNode("view",{class:"signature-section"},[e.createElementVNode("view",{class:"signature-text"},[e.createElementVNode("text",null,"出借人: 平安贷有限公司"),e.createElementVNode("text",null,"借款人: "+e.toDisplayString(e.unref(n).userName),1),e.createElementVNode("text",null,"签订日期: "+e.toDisplayString(e.unref(n).startTime),1)]),e.createElementVNode("image",{src:"/static/zh/htzh.png",class:"seal-image"})])]))}},[["__scopeId","data-v-91f48ffe"]]),g="/static/nav/toux.png",E=a({__name:"my",setup(t){function n(){uni.removeStorageSync("token"),uni.removeStorageSync("userInfo"),uni.clearStorageSync(),uni.navigateTo({url:"/pages/login/index"})}const a=uni.getStorageSync("userInfo");function l(e){"/pages/borrow/borrow"!==e&&"/pages/also/also"!==e?uni.navigateTo({url:e}):uni.switchTab({url:e})}return(t,r)=>(e.openBlock(),e.createElementBlock("view",{class:"page-container"},[e.createElementVNode("view",{class:"user-profile"},[e.createElementVNode("image",{class:"avatar",src:g,mode:"平安贷"}),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("text",{class:"username"},e.toDisplayString(e.unref(a).userName),1),e.createElementVNode("text",{class:"no"},e.toDisplayString(e.unref(a).phoneNumber),1)])]),e.createElementVNode("view",{class:"content-row"},[e.createElementVNode("view",{class:"content-item",onClick:r[0]||(r[0]=e=>l("/pages/privacy/index"))},[e.createElementVNode("image",{class:"content-img",src:"/static/nav/borrow-1.png",mode:"平安贷"}),e.createElementVNode("text",{class:"value"}," 隐私协议 ")]),e.createElementVNode("view",{class:"content-item",onClick:r[1]||(r[1]=e=>l("/pages/help/index"))},[e.createElementVNode("image",{class:"content-img",src:"/static/nav/borrow-2.png",mode:"平安贷"}),e.createElementVNode("text",{class:"value"}," 客户须知 ")]),e.createElementVNode("view",{class:"content-item",onClick:r[2]||(r[2]=e=>l("/pages/contract/index"))},[e.createElementVNode("image",{class:"content-img",src:"/static/nav/nav-4.png",mode:"平安贷"}),e.createElementVNode("text",{class:"value"}," 贷款合同 ")]),e.createElementVNode("view",{class:"content-item",onClick:r[3]||(r[3]=e=>l("/pages/userinfo/index"))},[e.createElementVNode("image",{class:"content-img",src:"/static/nav/nav-3.png",mode:"平安贷"}),e.createElementVNode("text",{class:"value"}," 个人信息 ")])]),e.createElementVNode("view",{class:"action-buttons"},[e.createElementVNode("button",{class:"btn btn-repay",onClick:n},"退出登录")])]))}},[["__scopeId","data-v-fb9b762d"]]),N=a({__name:"index",setup(t){const n=uni.getStorageSync("userInfo");return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"page-container"},[e.createElementVNode("view",{class:"user-profile"},[e.createElementVNode("image",{class:"avatar",src:g,mode:"平安贷"}),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("text",{class:"username"},"李易峰"),e.createElementVNode("text",{class:"no"},"15880888888")])]),e.createElementVNode("view",{class:"content-row"},[e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"姓名"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(n).userName),1)]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"身份证号"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(n).idCard),1)]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"手机号码"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(n).phoneNumber),1)]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"开户银行"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(n).payOpenBank),1)]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("text",{class:"label"},"银行卡号"),e.createElementVNode("text",{class:"value"},e.toDisplayString(e.unref(n).payBankNumber),1)])])]))}},[["__scopeId","data-v-404f06a8"]]);const V=a({},[["render",function(t,n){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"paragraph"}," 1、合同到期后甲方有权要求乙方立即还本付息,所产生的法律后果由乙方自负。 "),e.createElementVNode("view",{class:"paragraph"}," 2、借款方必须按合同规定的期限还本付息。逾期不还的部分,借款方有权限期追回借款并收取每天借款总金额的0.05%迟纳金费用。 "),e.createElementVNode("view",{class:"paragraph"}," 3、乙方如未按合同规定归还借款,乙方应当承担违约金以及因诉讼发生的律师费、诉讼费、差旅费等费用。 "),e.createElementVNode("view",{class:"paragraph"}," 4、当甲方认为借款人发生或可能发生影响偿还能力之情形时,甲方有权提前收回借款,借款人应及时返还,借款人及保证人不得以任何理由抗辩。 ")])}]]);__definePage("pages/login/index",l),__definePage("pages/index/index",r),__definePage("pages/privacy/index",o),__definePage("pages/borrow/borrow",s),__definePage("pages/also/also",c),__definePage("pages/alsoaccount/also",i),__definePage("pages/contract/index",p),__definePage("pages/my/my",E),__definePage("pages/userinfo/index",N),__definePage("pages/help/index",V);
/*!
   * pinia v2.1.7
   * (c) 2023 Eduardo San Martin Morote
   * @license MIT
   */
const v=Symbol();var f,x;(x=f||(f={})).direct="direct",x.patchObject="patch object",x.patchFunction="patch function";const y=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,w=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,b=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function h(e,n){if(!("__proto__"===e||"constructor"===e&&n&&"object"==typeof n&&"prototype"in n))return n;!function(e){t("warn","at node_modules/destr/dist/index.mjs:12",`[destr] Dropping "${e}" key to prevent prototype pollution.`)}(e)}function _(e,t){if(null==e)return;let n=e;for(let a=0;a<t.length;a++){if(null==n||null==n[t[a]])return;n=n[t[a]]}return n}function k(e,t,n){if(0===n.length)return t;const a=n[0];return n.length>1&&(t=k("object"==typeof e&&null!==e&&Object.prototype.hasOwnProperty.call(e,a)?e[a]:Number.isInteger(Number(n[1]))?[]:{},t,Array.prototype.slice.call(n,1))),Number.isInteger(Number(a))&&Array.isArray(e)?e.slice()[a]:Object.assign({},e,{[a]:t})}function S(e,t){if(null==e||0===t.length)return e;if(1===t.length){if(null==e)return e;if(Number.isInteger(t[0])&&Array.isArray(e))return Array.prototype.slice.call(e,0).splice(t[0],1);const n={};for(const t in e)n[t]=e[t];return delete n[t[0]],n}if(null==e[t[0]]){if(Number.isInteger(t[0])&&Array.isArray(e))return Array.prototype.concat.call([],e);const n={};for(const t in e)n[t]=e[t];return n}return k(e,S(e[t[0]],Array.prototype.slice.call(t,1)),[t[0]])}function D(e,t){return t.map((e=>e.split("."))).map((t=>[t,_(e,t)])).filter((e=>void 0!==e[1])).reduce(((e,t)=>k(e,t[1],t[0])),{})}function A(e,t){return t.map((e=>e.split("."))).reduce(((e,t)=>S(e,t)),e)}function I(e,{storage:n,serializer:a,key:l,debug:r,pick:o,omit:s,beforeHydrate:c,afterHydrate:i},u,m=!0){try{m&&(null==c||c(u));const t=n.getItem(l);if(t){const n=a.deserialize(t),l=o?D(n,o):n,r=s?A(l,s):l;e.$patch(r)}m&&(null==i||i(u))}catch(d){r&&t("error","at node_modules/pinia-plugin-persistedstate/dist/index.js:30","[pinia-plugin-persistedstate]",d)}}function B(e,{storage:n,serializer:a,key:l,debug:r,pick:o,omit:s}){try{const t=o?D(e,o):e,r=s?A(t,s):t,c=a.serialize(r);n.setItem(l,c)}catch(c){r&&t("error","at node_modules/pinia-plugin-persistedstate/dist/index.js:48","[pinia-plugin-persistedstate]",c)}}function T(e={}){return function(t){!function(e,t,n){const{pinia:a,store:l,options:{persist:r=n}}=e;if(!r)return;if(!(l.$id in a.state.value)){const e=a._s.get(l.$id.replace("__hot:",""));return void(e&&Promise.resolve().then((()=>e.$persist())))}const o=(Array.isArray(r)?r:!0===r?[{}]:[r]).map(t);l.$hydrate=({runHooks:t=!0}={})=>{o.forEach((n=>{I(l,n,e,t)}))},l.$persist=()=>{o.forEach((e=>{B(l.$state,e)}))},o.forEach((t=>{I(l,t,e),l.$subscribe(((e,n)=>B(n,t)),{detached:!0})}))}(t,(n=>({key:(e.key?e.key:e=>e)(n.key??t.store.$id),debug:n.debug??e.debug??!1,serializer:n.serializer??e.serializer??{serialize:e=>JSON.stringify(e),deserialize:e=>function(e,t={}){if("string"!=typeof e)return e;if('"'===e[0]&&'"'===e[e.length-1]&&-1===e.indexOf("\\"))return e.slice(1,-1);const n=e.trim();if(n.length<=9)switch(n.toLowerCase()){case"true":return!0;case"false":return!1;case"undefined":return;case"null":return null;case"nan":return Number.NaN;case"infinity":return Number.POSITIVE_INFINITY;case"-infinity":return Number.NEGATIVE_INFINITY}if(!b.test(e)){if(t.strict)throw new SyntaxError("[destr] Invalid JSON");return e}try{if(y.test(e)||w.test(e)){if(t.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(e,h)}return JSON.parse(e)}catch(a){if(t.strict)throw a;return e}}(e)},storage:n.storage??e.storage??window.localStorage,beforeHydrate:n.beforeHydrate,afterHydrate:n.afterHydrate,pick:n.pick,omit:n.omit})),e.auto??!1)}}const C={onLaunch:function(){},onShow:function(){},onHide:function(){}};const{app:F,Vuex:P,Pinia:$}=function(){const t=e.createVueApp(C),a=function(){const t=e.effectScope(!0),n=t.run((()=>e.ref({})));let a=[],l=[];const r=e.markRaw({install(e){r._a=e,e.provide(v,r),e.config.globalProperties.$pinia=r,l.forEach((e=>a.push(e))),l=[]},use(e){return this._a?a.push(e):l.push(e),this},_p:a,_a:null,_e:t,_s:new Map,state:n});return r}();return a.use(T({key:e=>`__persisted__${e}`,storage:{setItem(e,t){uni.setStorageSync(e,t)},getItem:e=>uni.getStorageSync(e)}})),t.use(a),t.mixin({async onLoad(){const e=uni.getStorageSync("token");if(!this.$page||!["pages/login/index"].includes(this.$page.route))if(e)try{n.get("/h5GetInfo",{}).then((e=>{uni.setStorageSync("userInfo",e.data)})).catch((e=>{}))}catch(t){}else uni.redirectTo({url:"/pages/login/index"})}}),{app:t}}();uni.Vuex=P,uni.Pinia=$,F.provide("__globalStyles",__uniConfig.styles),F._component.mpType="app",F._component.render=()=>{},F.mount("#app")}(Vue);
