
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"enablePullDownRefresh":false,"navigationBar":{"backgroundColor":"#ffffff","titleText":"平安贷","type":"default","titleColor":"#000000"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"平安贷","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.75","entryPagePath":"pages/login/index","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"tabBar":{"position":"bottom","color":"#818181","selectedColor":"#EF4F3F","borderStyle":"white","blurEffect":"none","fontSize":"10px","iconWidth":"24px","spacing":"3px","height":"50px","list":[{"text":"首页","pagePath":"pages/index/index","iconPath":"/static/tab/nav-index.png","selectedIconPath":"/static/tab/nav-index-on.png"},{"text":"借款","pagePath":"pages/borrow/borrow","iconPath":"/static/tab/nav-jk.png","selectedIconPath":"/static/tab/nav-jk-on.png"},{"text":"还款","pagePath":"pages/also/also","iconPath":"/static/tab/nav-hk.png","selectedIconPath":"/static/tab/nav-hk-on.png"},{"text":"我的","pagePath":"pages/my/my","iconPath":"/static/tab/nav-user.png","selectedIconPath":"/static/tab/nav-user-on.png"}],"backgroundColor":"#fff","selectedIndex":0,"shown":true},"fallbackLocale":"zh-Hans","locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/login/index","meta":{"isQuit":true,"isEntry":true,"navigationBar":{"backgroundColor":"white","titleText":"登陆","type":"default"},"isNVue":false}},{"path":"pages/index/index","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":0,"navigationBar":{"backgroundColor":"white","titleText":"首页","type":"default"},"isNVue":false}},{"path":"pages/privacy/index","meta":{"navigationBar":{"backgroundColor":"#f8f8f8","titleText":"隐私政策","type":"default"},"isNVue":false}},{"path":"pages/borrow/borrow","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":1,"navigationBar":{"backgroundColor":"white","titleText":"借款","style":"default","type":"default"},"isNVue":false}},{"path":"pages/also/also","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":2,"navigationBar":{"backgroundColor":"white","titleText":"还款","type":"default"},"isNVue":false}},{"path":"pages/alsoaccount/also","meta":{"navigationBar":{"titleText":"平台指定还款账户","type":"default"},"isNVue":false}},{"path":"pages/contract/index","meta":{"navigationBar":{"backgroundColor":"#f8f8f8","titleText":"合同","type":"default"},"isNVue":false}},{"path":"pages/my/my","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":3,"navigationBar":{"titleText":"我的","type":"default"},"isNVue":false}},{"path":"pages/userinfo/index","meta":{"navigationBar":{"backgroundColor":"white","titleText":"个人信息","type":"default"},"isNVue":false}},{"path":"pages/help/index","meta":{"navigationBar":{"backgroundColor":"#f8f8f8","titleText":"客户须知","type":"default"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  