import{r as e,g as a,c as l,w as s,i as n,o as t,a as o,b as u,n as c,s as i,d,e as r,f as p,I as f,h,j as g,k as v,l as _,m}from"./index-2UvccwQY.js";import{_ as x}from"./_plugin-vue_export-helper.BCo6x5W8.js";const k=x({__name:"index",setup(x){const k=e(!1),b=a("userInfo"),y=e((null==b?void 0:b.phoneNumber)||"");function I(e){k.value=e.detail.value.length>0,console.log("checkbox change",e.detail.value),console.log(k.value)}function j(){c({url:"/pages/privacy/index"})}function w(){var e;y.value?k.value?"string"==typeof(e=y.value)&&11===e.length&&/^1[3-9]\d{9}$/.test(e)?d.post("/h5Login",{username:y.value,password:y.value},{noAuth:!0}).then((e=>{r("token",e.data),d.get("/h5GetInfo",{}).then((e=>{r("userInfo",e.data),p({url:"/pages/index/index"}),console.log("登录成功",e)})).catch((e=>{}))})).catch((e=>{console.error("登录失败",e)})):i({title:"手机号码格式错误",icon:"none"}):i({title:"请先同意隐私政策",icon:"none"}):i({title:"请输入手机号",icon:"none"})}return(e,a)=>{const c=n,i=f,d=h,r=g,p=v,x=_,b=m;return t(),l(c,{class:"page-container"},{default:s((()=>[o(c,{class:"user-profile"}),o(c,{class:"user-login"},{default:s((()=>[o(c,{class:"login-type"},{default:s((()=>[o(c,{class:"title"},{default:s((()=>[u("平安贷")])),_:1})])),_:1}),o(c,{class:"login-content"},{default:s((()=>[o(i,{modelValue:y.value,"onUpdate:modelValue":a[0]||(a[0]=e=>y.value=e),type:"text",placeholder:"请输入手机号码",class:"uni-input-input"},null,8,["modelValue"]),o(c,{class:"checkbox-wrapper"},{default:s((()=>[o(x,{onChange:I},{default:s((()=>[o(p,{class:"checkbox-label"},{default:s((()=>[o(d,{checked:k.value,color:"#f98a1d",style:{transform:"scale(0.8)"}},null,8,["checked"]),o(r,null,{default:s((()=>[u("我已阅读并同意")])),_:1}),o(r,{class:"privacy-link",onClick:j},{default:s((()=>[u("《隐私政策》")])),_:1})])),_:1})])),_:1})])),_:1}),o(b,{class:"submit-button",onClick:w},{default:s((()=>[u("登 录")])),_:1})])),_:1})])),_:1})])),_:1})}}},[["__scopeId","data-v-d1deee18"]]);export{k as default};
