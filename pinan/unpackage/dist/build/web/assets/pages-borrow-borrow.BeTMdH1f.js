import{r as t,c as a,w as e,i as s,o as n,a as l,b as i,p as c,q as o,F as r,s as u,v as _,m as d,u as f,j as p}from"./index-2UvccwQY.js";import{_ as m}from"./_plugin-vue_export-helper.BCo6x5W8.js";const b=m({__name:"borrow",setup(m){const b=t([{titleLine1:"200000",titleLine2:"最高可借",img:"/static/nav/borrow-1.png"},{titleLine1:"0.25%",titleLine2:"利率低至",img:"/static/nav/borrow-2.png"},{titleLine1:"期限最长",titleLine2:"最长可至24个月",img:"/static/nav/nav-3.png"},{titleLine1:"灵活还款",titleLine2:"支付宝/微信/银行卡",img:"/static/nav/borrow-4.png"}]);function g(){u({title:"逾期暂不开放",icon:"none"})}return(t,u)=>{const m=_,L=s,v=p,w=d;return n(),a(L,{class:"page-container"},{default:e((()=>[l(L,{class:"banner"},{default:e((()=>[l(m,{src:"/assets/borrow-Be8I4GP9.png",mode:"aspectFill",class:"banner-img"}),l(L,{class:"banner-text"},{default:e((()=>[l(L,null,{default:e((()=>[i("最高可借额度")])),_:1}),l(L,null,{default:e((()=>[i("200000")])),_:1})])),_:1})])),_:1}),l(L,{class:"icon-row"},{default:e((()=>[(n(!0),c(r,null,o(b.value,((t,s)=>(n(),a(L,{class:"icon-item",key:s},{default:e((()=>[l(L,{class:"icon-circle"},{default:e((()=>[l(m,{src:t.img,mode:"aspectFit",class:"icon-img"},null,8,["src"])])),_:2},1024),l(L,{class:"icon-text"},{default:e((()=>[l(v,{class:"title-line1"},{default:e((()=>[i(f(t.titleLine1),1)])),_:2},1024),l(v,null,{default:e((()=>[i(f(t.titleLine2),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1}),l(L,{class:"action-buttons"},{default:e((()=>[l(w,{class:"btn btn-repay",onClick:g},{default:e((()=>[i("立即借款")])),_:1})])),_:1})])),_:1})}}},[["__scopeId","data-v-c9bbb9ff"]]);export{b as default};
