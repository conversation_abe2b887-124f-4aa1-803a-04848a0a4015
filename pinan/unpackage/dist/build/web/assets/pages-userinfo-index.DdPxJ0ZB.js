import{g as a,c as e,w as s,i as l,o as t,a as u,b as c,u as n,D as d,j as f}from"./index-2UvccwQY.js";import{_}from"./_plugin-vue_export-helper.BCo6x5W8.js";const o=_({__name:"index",setup(_){const o=a("userInfo");return(a,_)=>{const r=f,i=l;return t(),e(i,{class:"page-container"},{default:s((()=>[u(i,{class:"user-profile"},{default:s((()=>[u(i,{class:"user-info"},{default:s((()=>[u(r,{class:"username"},{default:s((()=>[c(n(d(o).userName.charAt(0)),1)])),_:1})])),_:1})])),_:1}),u(i,{class:"content-row"},{default:s((()=>[u(i,{class:"content-item"},{default:s((()=>[u(r,{class:"label"},{default:s((()=>[c("姓名")])),_:1}),u(r,{class:"value"},{default:s((()=>[c(n(d(o).userName),1)])),_:1})])),_:1}),u(i,{class:"content-item"},{default:s((()=>[u(r,{class:"label"},{default:s((()=>[c("身份证号")])),_:1}),u(r,{class:"value"},{default:s((()=>[c(n(d(o).idCard),1)])),_:1})])),_:1}),u(i,{class:"content-item"},{default:s((()=>[u(r,{class:"label"},{default:s((()=>[c("手机号码")])),_:1}),u(r,{class:"value"},{default:s((()=>[c(n(d(o).phoneNumber),1)])),_:1})])),_:1}),u(i,{class:"content-item"},{default:s((()=>[u(r,{class:"label"},{default:s((()=>[c("开户银行")])),_:1}),u(r,{class:"value"},{default:s((()=>[c(n(d(o).openBank),1)])),_:1})])),_:1}),u(i,{class:"content-item"},{default:s((()=>[u(r,{class:"label"},{default:s((()=>[c("银行卡号")])),_:1}),u(r,{class:"value"},{default:s((()=>[c(n(d(o).bankNumber),1)])),_:1})])),_:1})])),_:1})])),_:1})}}},[["__scopeId","data-v-d95412af"]]);export{o as default};
