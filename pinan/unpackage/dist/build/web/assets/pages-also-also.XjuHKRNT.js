import{r as a,g as l,c as e,w as s,i as t,o as u,a as n,b as c,u as d,t as o,n as f,v as _,j as v,m as i}from"./index-2UvccwQY.js";import{o as m}from"./uni-app.es.BCT1AvW8.js";import{_ as r}from"./_plugin-vue_export-helper.BCo6x5W8.js";const b=r({__name:"also",setup(r){const b=a({}),p=a(0);function g(){f({url:"/pages/contract/index"})}function x(){f({url:"/pages/alsoaccount/also"})}return m((()=>{const a=l("userInfo")||{};b.value=a,a.endTime?p.value=Math.ceil((Date.now()-new Date(a.endTime).getTime())/864e5):p.value=0})),(a,l)=>{const f=_,m=t,r=v,T=i;return u(),e(m,{class:"page-container"},{default:s((()=>[n(m,{class:"banner"},{default:s((()=>[n(f,{src:"/assets/also-DdMCl62v.png",mode:"aspectFill",class:"banner-img"}),n(m,{class:"banner-text"},{default:s((()=>[n(m,null,{default:s((()=>[c("应还金额")])),_:1}),n(m,null,{default:s((()=>[c(d(b.value.payableAmount),1)])),_:1})])),_:1})])),_:1}),n(m,{class:"content-row"},{default:s((()=>[n(m,{class:"content-item"},{default:s((()=>[n(r,{class:"label"},{default:s((()=>[c("借款人姓名")])),_:1}),n(r,{class:"value"},{default:s((()=>[c(d(b.value.userName),1)])),_:1})])),_:1}),n(m,{class:"content-item"},{default:s((()=>[n(r,{class:"label"},{default:s((()=>[c("身份证号")])),_:1}),n(r,{class:"value"},{default:s((()=>[c(d(b.value.idCard),1)])),_:1})])),_:1}),n(m,{class:"content-item"},{default:s((()=>[n(r,{class:"label"},{default:s((()=>[c("手机号码")])),_:1}),n(r,{class:"value"},{default:s((()=>[c(d(b.value.phoneNumber),1)])),_:1})])),_:1}),n(m,{class:"content-item"},{default:s((()=>[n(r,{class:"label"},{default:s((()=>[c("开户银行")])),_:1}),n(r,{class:"value"},{default:s((()=>[c(d(b.value.openBank),1)])),_:1})])),_:1}),n(m,{class:"content-item"},{default:s((()=>[n(r,{class:"label"},{default:s((()=>[c("下款卡号")])),_:1}),n(r,{class:"value"},{default:s((()=>[c(d(b.value.bankNumber),1)])),_:1})])),_:1}),n(m,{class:"content-item"},{default:s((()=>[n(r,{class:"label"},{default:s((()=>[c("下款时间")])),_:1}),n(r,{class:"value"},{default:s((()=>[c(d(b.value.startTime),1)])),_:1})])),_:1}),n(m,{class:"content-item"},{default:s((()=>[n(r,{class:"label"},{default:s((()=>[c("到期时间")])),_:1}),n(r,{class:"value"},{default:s((()=>[c(d(b.value.endTime),1)])),_:1})])),_:1}),n(m,{class:"content-item"},{default:s((()=>[n(r,{class:"label"},{default:s((()=>[c("逾期天数")])),_:1}),n(r,{class:"value"},{default:s((()=>[c(d(p.value<0?"未逾期":p.value+"天"),1)])),_:1})])),_:1}),n(m,{class:"content-item"},{default:s((()=>[n(r,{class:"label"},{default:s((()=>[c("贷款金额")])),_:1}),n(r,{class:"value"},{default:s((()=>[c(d(b.value.loanAmount),1)])),_:1})])),_:1}),n(m,{class:"content-item"},{default:s((()=>[n(r,{class:"label"},{default:s((()=>[c("逾期金额")])),_:1}),n(r,{class:"value"},{default:s((()=>[c(d(b.value.expectAmount),1)])),_:1})])),_:1}),n(m,{class:"content-item"},{default:s((()=>[n(r,{class:"label"},{default:s((()=>[c("应还金额")])),_:1}),n(r,{class:"value"},{default:s((()=>[c(d(b.value.payableAmount),1)])),_:1})])),_:1}),n(m,{class:"content-item"},{default:s((()=>[n(r,{class:"label"},{default:s((()=>[c("欠款状态")])),_:1}),n(r,{class:o(["value",{"status-overdue":p.value>0,"status-normal":2==b.value.loanInfoStatus||p.value<=0}])},{default:s((()=>[c(d(2==b.value.loanInfoStatus?"已还款":p.value<0?"未逾期":"已逾期"),1)])),_:1},8,["class"])])),_:1})])),_:1}),n(m,{class:"action-buttons"},{default:s((()=>[n(T,{class:"btn btn-detail",onClick:g},{default:s((()=>[c("查看合同")])),_:1}),n(T,{class:"btn btn-repay",onClick:x},{default:s((()=>[c("立即还款")])),_:1})])),_:1})])),_:1})}}},[["__scopeId","data-v-100d1496"]]);export{b as default};
