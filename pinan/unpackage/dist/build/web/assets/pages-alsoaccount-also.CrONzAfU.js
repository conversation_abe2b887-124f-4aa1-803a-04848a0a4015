import{g as a,r as e,c as l,w as s,i as t,o as n,a as u,p as c,q as o,F as r,C as p,D as d,E as i,b as f,u as m,G as v,v as _,j as b}from"./index-2UvccwQY.js";import{_ as g}from"./_plugin-vue_export-helper.BCo6x5W8.js";const k=g({__name:"also",setup(g){const k=a("userInfo"),y=e([{label:"开户姓名:",value:k.payOpenName||"请联系客服人员处理"},{label:"开户银行:",value:k.payOpenBank||"请联系客服人员处理"},{label:"银行卡号:",value:k.payBankNumber||"请联系客服人员处理"},{label:"开户地址:",value:k.payOpenAddress||"请联系客服人员处理"},{label:"备注信息:",value:k.payRemark||""}]),I=e(k.imageInfo&&k.imageInfo.length>0?k.imageInfo.split(","):[]);return(a,e)=>{const g=_,k=t,q=b;return n(),l(k,{class:"page-container"},{default:s((()=>[u(k,{class:"content-row"},{default:s((()=>[I.value.length>0?(n(),l(k,{key:0,class:"qrcode-list"},{default:s((()=>[(n(!0),c(r,null,o(I.value,((a,e)=>(n(),l(k,{key:e,class:"qrcode-item"},{default:s((()=>[u(g,{src:d(i)+a,mode:"aspectFit",class:"qrcode-img",onClick:a=>function(a){const e=I.value.map((a=>i+a));v({current:e[a],urls:e})}(e)},null,8,["src","onClick"])])),_:2},1024)))),128))])),_:1})):p("",!0),(n(!0),c(r,null,o(y.value,((a,e)=>(n(),l(k,{class:"content-item",key:e},{default:s((()=>[u(q,{class:"label"},{default:s((()=>[f(m(a.label),1)])),_:2},1024),u(q,{class:"value"},{default:s((()=>[f(m(a.value),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1})}}},[["__scopeId","data-v-3e3336df"]]);export{k as default};
