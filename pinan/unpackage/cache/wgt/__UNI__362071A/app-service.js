if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((n=>t.resolve(e()).then((()=>n))),(n=>t.resolve(e()).then((()=>{throw n}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.<PERSON>Int64Array,BigUint64Array=e.<PERSON>Uint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";const t=(e,t)=>{const n=e.__vccOpts||e;for(const[a,l]of t)n[a]=l;return n},n=t({__name:"index",setup(t){const n=e.ref([{title:"借款",img:"/static/nav/nav-1.png",url:"/pages/borrow/borrow"},{title:"还款",img:"/static/nav/nav-2.png",url:"/pages/also/also"},{title:"隐私协议",img:"/static/nav/nav-3.png",url:"/pages/privacy/index"},{title:"帮助",img:"/static/nav/nav-4.png",url:"/pages/help/index"}]);function a(){uni.navigateTo({url:"/pages/detail/index"})}function l(){uni.navigateTo({url:"/pages/repay/index"})}const r=e.ref(["欢迎来到平安贷","最新活动：限时优惠","请注意保护个人信息"]),o=e.ref({loanAmount:1e4,dueDate:"2025-07-24",overdueDays:13,overdueAmount:6900,repayAmount:4235});return(t,c)=>(e.openBlock(),e.createElementBlock("view",{class:"page-container"},[e.createElementVNode("swiper",{autoplay:"",circular:"","indicator-dots":"",class:"banner"},[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList([1,2,3,4],((t,n)=>e.createElementVNode("swiper-item",{key:n},[e.createElementVNode("image",{src:`/static/banner/index-${t}.png`,mode:"aspectFill",class:"banner-img"},null,8,["src"])]))),64))]),e.createElementVNode("view",{class:"notice-bar"},[e.createElementVNode("image",{src:"/static/horn.png",class:"horn-icon"}),e.createElementVNode("swiper",{autoplay:"",vertical:"",circular:"",interval:"2000",class:"notice-swiper"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(r.value,((t,n)=>(e.openBlock(),e.createElementBlock("swiper-item",{key:n},[e.createElementVNode("text",{class:"notice-text"},"📣"),e.createElementVNode("text",{class:"notice-text"},e.toDisplayString(t),1)])))),128))])]),e.createElementVNode("view",{class:"icon-row"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"icon-item",key:n,onClick:e=>{return n=t.url,void uni.navigateTo({url:n});var n}},[e.createElementVNode("view",{class:"icon-circle"},[e.createElementVNode("image",{src:t.img,mode:"aspectFit",class:"icon-img"},null,8,["src"])]),e.createElementVNode("text",null,e.toDisplayString(t.title),1)],8,["onClick"])))),128))]),e.createElementVNode("view",{class:"dh-text"}," 待还款项 "),e.createElementVNode("view",{class:"readonly"},[e.createElementVNode("view",{class:"readonly-form"},[e.createElementVNode("view",{class:e.normalizeClass(["status-watermark",o.value.overdueDays>0?"overdue":"normal"])},e.toDisplayString(o.value.overdueDays>0?"已逾期":"正常"),3),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"贷款金额"),e.createElementVNode("text",{class:"value"},e.toDisplayString(o.value.loanAmount),1)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"到期日期"),e.createElementVNode("text",{class:"value"},e.toDisplayString(o.value.dueDate),1)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"逾期天数"),e.createElementVNode("text",{class:"value"},e.toDisplayString(o.value.overdueDays)+" 天",1)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"逾期金额"),e.createElementVNode("text",{class:"value"},e.toDisplayString(o.value.overdueAmount),1)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"应还金额"),e.createElementVNode("text",{class:"value"},e.toDisplayString(o.value.repayAmount),1)])]),e.createElementVNode("view",{class:"action-buttons"},[e.createElementVNode("button",{class:"btn btn-detail",onClick:a},"查看详情"),e.createElementVNode("button",{class:"btn btn-repay",onClick:l},"立即还款")])]),e.createElementVNode("view",{class:"dh-text"}," 我要借款 "),e.createElementVNode("view",{class:"readonly"},[e.createElementVNode("view",{class:"readonly-form"},[e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"最多可借"),e.createElementVNode("text",{class:"value"},e.toDisplayString(o.value.loanAmount),1)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"利率低至"),e.createElementVNode("text",{class:"value"},e.toDisplayString(o.value.dueDate),1)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"最长期限"),e.createElementVNode("text",{class:"value"},e.toDisplayString(o.value.overdueDays)+" 天",1)])]),e.createElementVNode("view",{class:"action-buttons"},[e.createElementVNode("button",{class:"btn btn-detail",onClick:a},"查看详情"),e.createElementVNode("button",{class:"btn btn-repay",onClick:l},"立即还款")])])]))}},[["__scopeId","data-v-339efc84"]]);function a(e,t,...n){uni.__log__?uni.__log__(e,t,...n):console[e].apply(console,[...n,t])}const l=t({__name:"index",setup(t){const n=e.ref(!1),l=e.ref("");function r(e){n.value=e.detail.value.length>0,a("log","at pages/login/index.vue:14","checkbox change",e.detail.value),a("log","at pages/login/index.vue:15",n.value)}function o(){uni.navigateTo({url:"/pages/privacy/index"})}function c(){var e;n.value?l.value?(e=l.value,/^1[3-9]\d{9}$/.test(e)?a("log","at pages/login/index.vue:44","登录手机号:",l.value):uni.showToast({title:"手机号格式不正确",icon:"none"})):uni.showToast({title:"请输入手机号",icon:"none"}):uni.showToast({title:"请先同意隐私政策",icon:"none"})}return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"page-container"},[e.createElementVNode("view",{class:"user-profile"}),e.createElementVNode("view",{class:"user-login"},[e.createElementVNode("view",{class:"login-type"},[e.createElementVNode("view",{class:"title"},"平安贷")]),e.createElementVNode("view",{class:"login-content"},[e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[0]||(a[0]=e=>l.value=e),type:"text",placeholder:"请输入手机号码",class:"uni-input-input"},null,512),[[e.vModelText,l.value]]),e.createElementVNode("view",{class:"checkbox-wrapper"},[e.createElementVNode("checkbox-group",{onChange:r},[e.createElementVNode("label",{class:"checkbox-label"},[e.createElementVNode("checkbox",{checked:n.value,color:"#f98a1d",style:{transform:"scale(0.8)"}},null,8,["checked"]),e.createElementVNode("text",null,"我已阅读并同意"),e.createElementVNode("text",{class:"privacy-link",onClick:o},"《隐私政策》")])],32)]),e.createElementVNode("button",{class:"submit-button",onClick:c},"登 录")])])]))}},[["__scopeId","data-v-d401e80d"]]);const r=t({},[["render",function(t,n){return e.openBlock(),e.createElementBlock("view",{class:"privacy-page"},[e.createElementVNode("scroll-view",{"scroll-y":"",class:"privacy-content"},[e.createElementVNode("view",{class:"section"},[e.createElementVNode("text",{class:"section-title"},"隐私政策"),e.createElementVNode("text",null," 您的隐私对我们至关重要。本政策解释了我们会收集哪些信息、如何使用这些信息以及您对数据的控制权。请仔细阅读。 ")]),e.createElementVNode("view",{class:"section"},[e.createElementVNode("text",{class:"section-title"},"1. 我们收集的信息"),e.createElementVNode("view",{class:"subsection"},[e.createElementVNode("text",{class:"subsection-title"},"1.1 您直接提供的信息"),e.createElementVNode("text",null,"• 账户注册：姓名、邮箱、手机号等"),e.createElementVNode("text",null,"• 服务交互：订单记录、反馈内容、联系方式等")]),e.createElementVNode("view",{class:"subsection"},[e.createElementVNode("text",{class:"subsection-title"},"1.2 自动收集的信息"),e.createElementVNode("text",null,"• 设备信息：设备型号、操作系统、唯一设备标识符（如IMEI）"),e.createElementVNode("text",null,"• 使用数据：访问时间、功能使用频率、崩溃日志等"),e.createElementVNode("text",null,"• 位置信息（仅限相关服务）：GPS或IP地址定位（需用户授权）")]),e.createElementVNode("view",{class:"subsection"},[e.createElementVNode("text",{class:"subsection-title"},"1.3 第三方来源的信息"),e.createElementVNode("text",null,"• 通过社交媒体账号登录时，可能获取公开资料（如头像、昵称）")])]),e.createElementVNode("view",{class:"section"},[e.createElementVNode("text",{class:"section-title"},"2. 我们如何使用信息"),e.createElementVNode("text",null,"• 提供和优化服务（如账户管理、客服支持）"),e.createElementVNode("text",null,"• 安全保障：检测异常活动、防止欺诈"),e.createElementVNode("text",null,"• 统计分析：改进产品功能（匿名化处理数据）"),e.createElementVNode("text",null,"• 营销推广（需用户单独同意）：发送促销信息")]),e.createElementVNode("view",{class:"section"},[e.createElementVNode("text",{class:"section-title"},"3. 数据共享与披露"),e.createElementVNode("text",null,"• 第三方服务商：支付处理、云存储等（需签署保密协议）"),e.createElementVNode("text",null,"• 法律要求：响应法院指令或政府调查"),e.createElementVNode("text",null,"• 商业转让：如发生并购，用户数据可能转移")]),e.createElementVNode("view",{class:"section"},[e.createElementVNode("text",{class:"section-title"},"4. 数据存储与安全"),e.createElementVNode("text",null,"• 存储期限：满足法律要求或业务所需的最短时间"),e.createElementVNode("text",null,"• 安全措施：加密传输、访问控制、定期审计")]),e.createElementVNode("view",{class:"section"},[e.createElementVNode("text",{class:"section-title"},"5. 您的权利"),e.createElementVNode("text",null,"• 访问、更正或删除个人信息"),e.createElementVNode("text",null,"• 撤回同意（部分功能可能受限）"),e.createElementVNode("text",null,"• 注销账户（通过设置或联系客服）")]),e.createElementVNode("view",{class:"section"},[e.createElementVNode("text",{class:"section-title"},"6. 儿童隐私"),e.createElementVNode("text",null,"• 服务不面向13周岁（或所在国法定年龄）以下用户"),e.createElementVNode("text",null,"• 如意外收集儿童数据，将立即删除")]),e.createElementVNode("view",{class:"section"},[e.createElementVNode("text",{class:"section-title"},"7. 政策更新"),e.createElementVNode("text",null,"• 修改政策会通过App内公告或邮件通知"),e.createElementVNode("text",null,"• 继续使用视为接受更新条款")])])])}],["__scopeId","data-v-1708030c"]]),o=t({__name:"borrow",setup(t){const n=e.ref([{titleLine1:"200000",titleLine2:"最高可借",img:"/static/nav/borrow-1.png"},{titleLine1:"0.25%",titleLine2:"利率低至",img:"/static/nav/borrow-2.png"},{titleLine1:"期限最长",titleLine2:"最长可至24个月",img:"/static/nav/nav-3.png"},{titleLine1:"灵活还款",titleLine2:"支付宝/微信/银行卡",img:"/static/nav/borrow-4.png"}]);function a(){}return(t,l)=>(e.openBlock(),e.createElementBlock("view",{class:"page-container"},[e.createElementVNode("view",{class:"banner"},[e.createElementVNode("image",{src:"/static/banner/borrow.png",mode:"aspectFill",class:"banner-img"}),e.createElementVNode("view",{class:"banner-text"},[e.createElementVNode("view",null,"最高可借额度"),e.createElementVNode("view",null,"200000")])]),e.createElementVNode("view",{class:"icon-row"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"icon-item",key:n},[e.createElementVNode("view",{class:"icon-circle"},[e.createElementVNode("image",{src:t.img,mode:"aspectFit",class:"icon-img"},null,8,["src"])]),e.createElementVNode("view",{class:"icon-text"},[e.createElementVNode("text",{class:"title-line1"},e.toDisplayString(t.titleLine1),1),e.createElementVNode("text",null,e.toDisplayString(t.titleLine2),1)])])))),128))]),e.createElementVNode("view",{class:"action-buttons"},[e.createElementVNode("button",{class:"btn btn-repay",onClick:a},"立即借款")])]))}},[["__scopeId","data-v-b48bfe2e"]]),c=t({__name:"also",setup(t){function n(){uni.navigateTo({url:"/pages/detail/index"})}function a(){uni.navigateTo({url:"/pages/repay/index"})}const l=e.ref([{label:"借款人姓名",value:"李易峰"},{label:"身份证号",value:"3658957888"},{label:"手机号码",value:"15880888888"},{label:"开户银行",value:"中国银行"},{label:"下款卡号",value:"6895591306221596689"},{label:"下款时间",value:"2025-07-01 12:00:00"},{label:"到期时间",value:"2025-07-24 12:00:00"},{label:"逾期天数",value:13},{label:"贷款金额",value:"10000.00"},{label:"逾期金额",value:"6900.00"},{label:"应还金额",value:"4235.00"},{label:"欠款状态",value:"已逾期"}]);return(t,r)=>(e.openBlock(),e.createElementBlock("view",{class:"page-container"},[e.createElementVNode("view",{class:"banner"},[e.createElementVNode("image",{src:"/static/banner/also.png",mode:"aspectFill",class:"banner-img"}),e.createElementVNode("view",{class:"banner-text"},[e.createElementVNode("view",null,"应还金额"),e.createElementVNode("view",null,"4235.00")])]),e.createElementVNode("view",{class:"content-row"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"content-item",key:n},[e.createElementVNode("text",{class:"label"},e.toDisplayString(t.label),1),e.createElementVNode("text",{class:e.normalizeClass(["value",{"status-overdue":"欠款状态"===t.label&&"已逾期"===t.value,"status-normal":"欠款状态"===t.label&&"正常"===t.value}])},e.toDisplayString(t.value),3)])))),128))]),e.createElementVNode("view",{class:"action-buttons"},[e.createElementVNode("button",{class:"btn btn-detail",onClick:n},"查看合同"),e.createElementVNode("button",{class:"btn btn-repay",onClick:a},"立即还款")])]))}},[["__scopeId","data-v-a03c4626"]]),s=t({__name:"my",setup(t){function n(){}return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"page-container"},[e.createElementVNode("view",{class:"user-profile"},[e.createElementVNode("image",{class:"avatar",src:"/static/nav/toux.png",mode:"平安贷"}),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("text",{class:"username"},"李易峰"),e.createElementVNode("text",{class:"no"},"15880888888")])]),e.createElementVNode("view",{class:"content-row"},[e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("image",{class:"content-img",src:"/static/nav/borrow-1.png",mode:"平安贷"}),e.createElementVNode("text",{class:"value"}," 隐私协议 ")]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("image",{class:"content-img",src:"/static/nav/borrow-2.png",mode:"平安贷"}),e.createElementVNode("text",{class:"value"}," 客户须知 ")]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("image",{class:"content-img",src:"/static/nav/nav-4.png",mode:"平安贷"}),e.createElementVNode("text",{class:"value"}," 贷款合同 ")]),e.createElementVNode("view",{class:"content-item"},[e.createElementVNode("image",{class:"content-img",src:"/static/nav/nav-3.png",mode:"平安贷"}),e.createElementVNode("text",{class:"value"}," 个人信息 ")])]),e.createElementVNode("view",{class:"action-buttons"},[e.createElementVNode("button",{class:"btn btn-repay",onClick:n},"退出登录")])]))}},[["__scopeId","data-v-e41838e6"]]);__definePage("pages/index/index",n),__definePage("pages/login/index",l),__definePage("pages/privacy/index",r),__definePage("pages/borrow/borrow",o),__definePage("pages/also/also",c),__definePage("pages/my/my",s);
/*!
   * pinia v2.1.7
   * (c) 2023 Eduardo San Martin Morote
   * @license MIT
   */
const i=Symbol();var u,d;(d=u||(u={})).direct="direct",d.patchObject="patch object",d.patchFunction="patch function";const m=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,p=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,v=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function E(e,t){if(!("__proto__"===e||"constructor"===e&&t&&"object"==typeof t&&"prototype"in t))return t;!function(e){a("warn","at node_modules/destr/dist/index.mjs:12",`[destr] Dropping "${e}" key to prevent prototype pollution.`)}(e)}function N(e,t){if(null==e)return;let n=e;for(let a=0;a<t.length;a++){if(null==n||null==n[t[a]])return;n=n[t[a]]}return n}function g(e,t,n){if(0===n.length)return t;const a=n[0];return n.length>1&&(t=g("object"==typeof e&&null!==e&&Object.prototype.hasOwnProperty.call(e,a)?e[a]:Number.isInteger(Number(n[1]))?[]:{},t,Array.prototype.slice.call(n,1))),Number.isInteger(Number(a))&&Array.isArray(e)?e.slice()[a]:Object.assign({},e,{[a]:t})}function V(e,t){if(null==e||0===t.length)return e;if(1===t.length){if(null==e)return e;if(Number.isInteger(t[0])&&Array.isArray(e))return Array.prototype.slice.call(e,0).splice(t[0],1);const n={};for(const t in e)n[t]=e[t];return delete n[t[0]],n}if(null==e[t[0]]){if(Number.isInteger(t[0])&&Array.isArray(e))return Array.prototype.concat.call([],e);const n={};for(const t in e)n[t]=e[t];return n}return g(e,V(e[t[0]],Array.prototype.slice.call(t,1)),[t[0]])}function y(e,t){return t.map((e=>e.split("."))).map((t=>[t,N(e,t)])).filter((e=>void 0!==e[1])).reduce(((e,t)=>g(e,t[1],t[0])),{})}function f(e,t){return t.map((e=>e.split("."))).reduce(((e,t)=>V(e,t)),e)}function b(e,{storage:t,serializer:n,key:l,debug:r,pick:o,omit:c,beforeHydrate:s,afterHydrate:i},u,d=!0){try{d&&(null==s||s(u));const a=t.getItem(l);if(a){const t=n.deserialize(a),l=o?y(t,o):t,r=c?f(l,c):l;e.$patch(r)}d&&(null==i||i(u))}catch(m){r&&a("error","at node_modules/pinia-plugin-persistedstate/dist/index.js:30","[pinia-plugin-persistedstate]",m)}}function w(e,{storage:t,serializer:n,key:l,debug:r,pick:o,omit:c}){try{const a=o?y(e,o):e,r=c?f(a,c):a,s=n.serialize(r);t.setItem(l,s)}catch(s){r&&a("error","at node_modules/pinia-plugin-persistedstate/dist/index.js:48","[pinia-plugin-persistedstate]",s)}}function x(e={}){return function(t){!function(e,t,n){const{pinia:a,store:l,options:{persist:r=n}}=e;if(!r)return;if(!(l.$id in a.state.value)){const e=a._s.get(l.$id.replace("__hot:",""));return void(e&&Promise.resolve().then((()=>e.$persist())))}const o=(Array.isArray(r)?r:!0===r?[{}]:[r]).map(t);l.$hydrate=({runHooks:t=!0}={})=>{o.forEach((n=>{b(l,n,e,t)}))},l.$persist=()=>{o.forEach((e=>{w(l.$state,e)}))},o.forEach((t=>{b(l,t,e),l.$subscribe(((e,n)=>w(n,t)),{detached:!0})}))}(t,(n=>({key:(e.key?e.key:e=>e)(n.key??t.store.$id),debug:n.debug??e.debug??!1,serializer:n.serializer??e.serializer??{serialize:e=>JSON.stringify(e),deserialize:e=>function(e,t={}){if("string"!=typeof e)return e;if('"'===e[0]&&'"'===e[e.length-1]&&-1===e.indexOf("\\"))return e.slice(1,-1);const n=e.trim();if(n.length<=9)switch(n.toLowerCase()){case"true":return!0;case"false":return!1;case"undefined":return;case"null":return null;case"nan":return Number.NaN;case"infinity":return Number.POSITIVE_INFINITY;case"-infinity":return Number.NEGATIVE_INFINITY}if(!v.test(e)){if(t.strict)throw new SyntaxError("[destr] Invalid JSON");return e}try{if(m.test(e)||p.test(e)){if(t.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(e,E)}return JSON.parse(e)}catch(a){if(t.strict)throw a;return e}}(e)},storage:n.storage??e.storage??window.localStorage,beforeHydrate:n.beforeHydrate,afterHydrate:n.afterHydrate,pick:n.pick,omit:n.omit})),e.auto??!1)}}const _={onLaunch:function(){},onShow:function(){},onHide:function(){}},k={toast(e="数据加载失败！",t="none"){uni.showToast({title:e,icon:t,mask:!0})}};uni.utils=k;const{app:h,Vuex:A,Pinia:I}=function(){const t=e.createVueApp(_),n=function(){const t=e.effectScope(!0),n=t.run((()=>e.ref({})));let a=[],l=[];const r=e.markRaw({install(e){r._a=e,e.provide(i,r),e.config.globalProperties.$pinia=r,l.forEach((e=>a.push(e))),l=[]},use(e){return this._a?a.push(e):l.push(e),this},_p:a,_a:null,_e:t,_s:new Map,state:n});return r}();return n.use(x({key:e=>`__persisted__${e}`,storage:{setItem(e,t){uni.setStorageSync(e,t)},getItem:e=>uni.getStorageSync(e)}})),t.use(n),{app:t}}();uni.Vuex=A,uni.Pinia=I,h.provide("__globalStyles",__uniConfig.styles),h._component.mpType="app",h._component.render=()=>{},h.mount("#app")}(Vue);
