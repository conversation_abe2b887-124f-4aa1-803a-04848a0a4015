import {createSSRApp} from 'vue'
import {createPinia} from 'pinia'
import {createPersistedState} from 'pinia-plugin-persistedstate'
import App from './App'
import './utils/utils'
import request from "./utils/request";

export function createApp() {
  const app = createSSRApp(App)
  const pinia = createPinia()

  // pinia 持久化
  pinia.use(createPersistedState({
    key: (id) => `__persisted__${id}`,
    storage: {
      setItem(key, value) {
        uni.setStorageSync(key, value)
      },
      getItem(key) {
        return uni.getStorageSync(key)
      }
    }
  }))

  app.use(pinia)

// 全局混入拦截页面访问
  app.mixin({
    methods: {
      async checkAuth() {
        const token = uni.getStorageSync('token')
        // 排除登录页，避免死循环
        if (this.$page && ['pages/login/index'].includes(this.$page.route)) {
          return
        }

        // 如果没 token 直接跳转登录
        if (!token) {
          uni.redirectTo({url: '/pages/login/index'})
          return
        }

        // 如果有 token，则调用后端验证一次
        try {
          request.get('/h5GetInfo', {})
            .then(res => {
              uni.removeStorageSync('userInfo')
              uni.setStorageSync('userInfo', res.data)
            })
            .catch(err => {
            })
        } catch (e) {
        }
      }
    },

    onLoad() {
      this.checkAuth()
    },

    onShow() {
      this.checkAuth()
    }
  })


  return {
    app
  }
}
