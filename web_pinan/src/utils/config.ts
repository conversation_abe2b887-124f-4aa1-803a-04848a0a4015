// @ts-nocheck
import {getConfigByKey} from '@/services/system/config';
import {getTenantConfigByKey} from '@/services/system/tenantConfig';
import userStore from '@/store/user';

export const getAdminConfig = async (key) => {
  const res = await getConfigByKey(key);
  if (res.code === 200) {
    return {[key]: res.msg};
  }
  return {[key]: ''};
};

export const getTenantConfig = async (tenantId, key) => {
  const res = await getTenantConfigByKey(tenantId, key);
  if (res.code === 200) {
    return {[key]: res.msg};
  }
  return {[key]: ''};
};

export const getAllConfig = (
  keyList: string[],
  userInfo = userStore.currentUser,
): { [key: string]: string } => {
  const promiseTasks: any[] = [];
  if (userInfo.isAdmin) {
    keyList.forEach((key) => {
      promiseTasks.push(getAdminConfig(key));
    });
  } else {
    keyList.forEach((key) => {
      promiseTasks.push(getTenantConfig(userInfo.tenantId, key));
    });
  }
  return Promise.all(promiseTasks)
    .then((res) => {
      return Object.assign({}, ...res);
    })
    .catch((err) => {
      console.log(err);
      return {};
    });
};
