import moment from 'moment';

export const formatQueryTime = (value: string) => {
  switch (value) {
    case '1':
      return [moment().startOf('day'), moment().endOf('day')];
    case '2':
      return [moment().subtract(1, 'day').startOf('day'), moment().subtract(1, 'day').endOf('day')];
    case '3':
      return [moment().subtract(2, 'day').startOf('day'), moment().subtract(2, 'day').endOf('day')];
    case '4':
      return [moment().subtract(2, 'day').startOf('day'), moment().endOf('day')];
    case '5':
      return [moment().subtract(6, 'day').startOf('day'), moment().endOf('day')];
    case '6':
      return [moment().startOf('week').startOf('day'), moment().endOf('day')];
    case '7':
      return [moment().startOf('month').startOf('day'), moment().endOf('day')];
    default:
      break;
  }
};
