// @ts-nocheck
import {Space, Spin} from 'antd';
import ReactDOM from 'react-dom';

// 当前正在请求的数量
let requestCount = 0

// 隐藏loading
export function hideLoading() {
  if (requestCount > 0) {
    requestCount--
  }
  if (document.getElementById('myloading') && requestCount === 0) {
    // @ts-ignore
    document.body.removeChild(document.getElementById('myloading'))
  }
}

/**
 * @param count 请求次数 默认为1
 * @param content 加载提示内容
 */
export function loading(count: number, content: string) {
  // eslint-disable-next-line no-param-reassign
  content = content ? content : "加载中..."
  if (requestCount === 0) {
    const dom = document.createElement('div');
    dom.setAttribute('id', 'myloading')
    document.body.appendChild(dom)
    // eslint-disable-next-line react/no-deprecated
    ReactDOM.render(
      <Space direction="vertical" style={{width: '100%', zIndex: 100}}>
        <Spin tip={content} size="large">
          <alert/>
        </Spin>
      </Space>, dom)
  }
  // 注意这里的requestCount不能用++，因为可能同时请求多个接口
  // eslint-disable-next-line no-param-reassign
  count = count ? count : 1
  requestCount += count
}


