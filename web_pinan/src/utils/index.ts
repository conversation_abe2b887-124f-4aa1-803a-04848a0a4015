// @ts-nocheck
import _ from 'lodash';

export const stringToBoolean = (value: any, key: string[] | string) => {
  if (typeof value !== 'object' && !Object.keys(value).length) {
    throw new Error('类型不正确');
  }
  const keyList = typeof key === 'string' ? [key] : key;
  const obj = _.cloneDeep(value);
  keyList.forEach((k) => {
    if (typeof value[k] === 'boolean') {
      obj[k] = obj[k] ? '1' : '2';
    }
    if (typeof value[k] === 'string') {
      obj[k] = obj[k] === '1';
    }
  });
  return obj;
};

export const simpleStringToBoolean = (value) => {
  if (typeof value === 'string') {
    return value === '1';
  }
  if (typeof value === 'boolean') {
    return value ? '1' : '2';
  }
  return false;
};

export const formatNumber = (value: number, dit = 2) => {
  return Number(value || 0).toFixed(dit);
};

export const ipv4Regex = (value) => {
  return /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(
    value,
  );
};

export const getCurrencyLogo = (value, list = {}) => {
  if (value && Object.keys(list).length) {
    const item = list[value]?.label;
    return item?.split('|')[1].trim();
  } else {
    return '';
  }
};
export const getCurrencyText = (value, list = {}) => {
  if (value && Object.keys(list).length) {
    const item = list[value]?.label;
    return item?.split('|')[2].trim();
  } else {
    return '';
  }
};

export const tableTotal = (
  list: any[],
  {
    titleKey,
    keys,
    averageKey,
  }: { titleKey: string | string[]; keys: string[]; averageKey?: string[] },
) => {
  const keyList = Array.isArray(titleKey) ? titleKey : [titleKey];
  const item: any = {
    isTotal: true,
  };
  keyList.forEach((key) => {
    item[key] = '合计';
  });
  keys.forEach((key) => {
    item[key] = _.sumBy(list, key);
  });
  averageKey?.forEach((key) => {
    item[key] = _.meanBy(list, key);
  });
  return list.concat(item);
};

export function generateRandomString(length = 16) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

export function removeEmptyParams(params) {
  // Create a new object to store the filtered parameters
  const filteredParams = {};

  // Iterate over the original parameters
  for (const key in params) {
    // Check if the parameter value is not empty
    if (params[key] !== '' && params[key] !== undefined && params[key] !== null) {
      // Add the parameter to the filtered object
      filteredParams[key] = params[key];
    }
  }

  // Return the filtered object
  return filteredParams;
}
