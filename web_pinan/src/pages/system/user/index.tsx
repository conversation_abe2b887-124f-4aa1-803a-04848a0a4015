//@ts-nocheck
import React, {useEffect, useRef, useState} from 'react';
import {FormattedMessage, useAccess, useIntl, useModel} from '@umijs/max';
import {Button, FormInstance, Switch} from 'antd';
import {message, modal as Modal} from '@/EntryComponent';
import {ActionType, FooterToolbar, PageContainer, ProColumns, ProTable,} from '@ant-design/pro-components';
import {DeleteOutlined, ExclamationCircleOutlined, PlusOutlined,} from '@ant-design/icons';
import {
  addUser,
  changeUserStatus,
  exportUser,
  getDeptTree,
  getUser,
  getUserList,
  removeUser,
  resetUserPwd,
  updateAuthRole,
  updateUser,
} from '@/services/system/user';
import UpdateForm from './edit';
import {getDictValueEnum} from '@/services/system/dict';
import {DataNode} from 'antd/es/tree';
import ResetPwd from './components/ResetPwd';
import {getRoleList} from '@/services/system/role';
import AuthRoleForm from './components/AuthRole';


/**
 * 添加节点
 *
 * @param fields
 */
const handleAdd = async (fields: API.System.User) => {
  // const hide = message.loading('正在添加');
  try {
    await addUser({...fields});
    // hide();
    message.success('创建成功');
    return true;
  } catch (error) {
    // hide();
    // message.error('添加失败请重试！');
    return false;
  }
};

/**
 * 更新节点
 *
 * @param fields
 */
const handleUpdate = async (fields: API.System.User) => {
  // const hide = message.loading('正在配置');
  try {
    await updateUser(fields);
    // hide();
    message.success('编辑成功');
    return true;
  } catch (error) {
    // hide();
    // message.error('配置失败请重试！');
    return false;
  }
};

/**
 * 删除节点
 *
 * @param selectedRows
 */
const handleRemove = async (selectedRows: API.System.User[]) => {
  // const hide = message.loading('正在删除');
  if (!selectedRows) return true;
  try {
    await removeUser(selectedRows.map((row) => row.userId).join(','));
    // hide();
    message.success('删除成功');
    return true;
  } catch (error) {
    // hide();
    // message.error('删除失败，请重试');
    return false;
  }
};

const handleRemoveOne = async (selectedRow: API.System.User) => {
  // const hide = message.loading('正在删除');
  if (!selectedRow) return true;
  try {
    const params = [selectedRow.userId];
    await removeUser(params.join(','));
    // hide();
    message.success('删除成功');
    return true;
  } catch (error) {
    // hide();
    // message.error(error.msg);
    return false;
  }
};

/**
 * 导出数据
 *
 *
 */
const handleExport = async (formTableRef) => {
  const params = formTableRef.current.getFieldFormatValue();
  const hide = message.loading('正在导出');
  try {
    await exportUser(params, 'user');
    hide();
    message.success('导出成功');
    return true;
  } catch (error) {
    hide();
    // message.error('导出失败，请重试');
    return false;
  }
};

const UserTableList: React.FC = () => {
  const formTableRef = useRef<FormInstance>();

  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [resetPwdModalVisible, setResetPwdModalVisible] = useState<boolean>(false);
  const [authRoleModalVisible, setAuthRoleModalVisible] = useState<boolean>(false);

  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.System.User>();
  const [selectedRows, setSelectedRows] = useState<API.System.User[]>([]);

  const [selectDept, setSelectDept] = useState<any>({id: 0});
  const [sexOptions, setSexOptions] = useState<any>([]);
  const [statusOptions, setStatusOptions] = useState<any>([]);

  const [postIds, setPostIds] = useState<number[]>();
  const [postList, setPostList] = useState<any[]>();
  const [roleIds, setRoleIds] = useState<number[]>();
  const [roleList, setRoleList] = useState<any[]>();
  const [deptTree, setDeptTree] = useState<DataNode[]>();

  const access = useAccess();
  const {initialState} = useModel('@@initialState');

  /** 国际化配置 */
  const intl = useIntl();

  useEffect(() => {
    getDictValueEnum('sys_user_sex').then((data) => {
      setSexOptions(data);
    });
    getDictValueEnum('sys_normal_disable').then((data) => {
      setStatusOptions(data);
    });
  }, []);

  const showChangeStatusConfirm = (record: API.System.User) => {
    let text = record.status === '1' ? '启用' : '停用';
    const newStatus = record.status === '0' ? '1' : '0';
    Modal.confirm({
      title: `确认要${text}${record.userName}用户吗？`,
      onOk() {
        return changeUserStatus(record.userId, newStatus)
          .then((resp) => {
            if (resp.code === 200) {
              message.open({
                type: 'success',
                content: '更新成功！',
              });
              actionRef.current?.reload();
            } else {
              message.open({
                type: 'error',
                content: '更新失败！',
              });
            }
          })
          .catch(() => {
            Promise.reject();
          });
      },
    });
  };

  const fetchUserInfo = async (userId: number) => {
    const {data: res} = await getUser(userId);
    setPostIds(res.postIds);
    setPostList(
      res.posts.map((item: any) => {
        return {
          value: item.postId,
          label: item.postName,
        };
      }),
    );
    setRoleIds(res.roleIds);
    const list = res.roles.map((item: any) => {
      return {
        value: item.roleId,
        label: item.roleName,
      };
    });
    setRoleList(list);
  };

  const columns: ProColumns<API.System.User>[] = [
    {
      title: <FormattedMessage id="system.user.user_name" defaultMessage="用户账号"/>,
      dataIndex: 'userName',
      valueType: 'text',
    },
    {
      title: <FormattedMessage id="system.user.nick_name" defaultMessage="用户昵称"/>,
      dataIndex: 'nickName',
      valueType: 'text',
    },
    {
      title: '用户类型',
      dataIndex: 'userType',
      valueType: 'text',
      hideInSearch: true,
      render(dom) {
        return dom === '00' ? '系统用户' : '商户';
      },
    },
    {
      title: '最后登录IP',
      dataIndex: 'loginIp',
      valueType: 'text',
    },
    {
      title: '最后登录时间',
      dataIndex: 'loginDate',
      valueType: 'text',
      hideInSearch: true,
    },
    {
      title: "创建时间",
      dataIndex: 'createTime',
      valueType: 'dateRange',
      render: (_, record) => {
        return <span>{record.createTime.toString()} </span>;
      },
      search: {
        transform: (value) => {
          return {
            'params[beginTime]': value[0],
            'params[endTime]': value[1],
          };
        },
      },
    },
    {
      title: <FormattedMessage id="system.user.status" defaultMessage="帐号状态"/>,
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: statusOptions,
      render: (_, record) => {
        console.log(initialState?.currentUser);
        return (
          <Switch
            checked={record.status === '0'}
            checkedChildren="启用"
            unCheckedChildren="停用"
            defaultChecked
            disabled={
              initialState?.currentUser?.userId === record.userId || record.userType === '1'
            }
            onClick={() => showChangeStatusConfirm(record)}
          />
        );
      },
    },
    {
      title: <FormattedMessage id="pages.searchTable.titleOption" defaultMessage="操作"/>,
      dataIndex: 'option',
      width: '220px',
      valueType: 'option',
      render: (_, record) =>
        initialState?.currentUser?.userId !== record.userId && record.userType !== '1'
          ? [
            <Button
              type="link"
              size="small"
              key="edit"
              hidden={!access.hasPerms('system:user:edit')}
              onClick={async () => {
                fetchUserInfo(record.userId);
                const treeData = await getDeptTree({});
                setDeptTree(treeData);
                setModalVisible(true);
                setCurrentRow(record);
              }}
            >
              编辑
            </Button>,
            <Button
              type="link"
              size="small"
              key="edit"
              hidden={!access.hasPerms('system:user:edit')}
              onClick={async () => {
                setResetPwdModalVisible(true);
                setCurrentRow(record);
              }}
            >
              重置密码
            </Button>,
            <Button
              type="link"
              size="small"
              danger
              key="batchRemove"
              hidden={!access.hasPerms('system:user:remove')}
              onClick={async () => {
                Modal.confirm({
                  title: '删除',
                  content: '确定删除该项吗？',
                  okText: '确认',
                  cancelText: '取消',
                  onOk: async () => {
                    const success = await handleRemoveOne(record);
                    if (success) {
                      if (actionRef.current) {
                        actionRef.current.reload();
                      }
                    }
                  },
                });
              }}
            >
              删除
            </Button>,
            // <Dropdown
            //   key="more"
            //   menu={{
            //     items: [
            //       {
            //         label: (
            //           <FormattedMessage
            //             id="system.user.reset.password"
            //             defaultMessage="密码重置"
            //           />
            //         ),
            //         key: 'reset',
            //         disabled: !access.hasPerms('system:user:edit'),
            //       },
            //       {
            //         label: '分配角色',
            //         key: 'authRole',
            //         disabled: !access.hasPerms('system:user:edit'),
            //       },
            //     ],
            //     onClick: ({ key }) => {
            //       if (key === 'reset') {
            //         setResetPwdModalVisible(true);
            //         setCurrentRow(record);
            //       } else if (key === 'authRole') {
            //         fetchUserInfo(record.userId);
            //         setAuthRoleModalVisible(true);
            //         setCurrentRow(record);
            //       }
            //     },
            //   }}
            // >
            //   <a onClick={(e) => e.preventDefault()}>
            //     <Space>
            //       <DownOutlined />
            //       更多
            //     </Space>
            //   </a>
            // </Dropdown>,
          ]
          : [],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.System.User>
        actionRef={actionRef}
        formRef={formTableRef}
        rowKey="userId"
        key="userList"
        scroll={{x: 'max-content'}}
        toolBarRender={() => [
          <Button
            type="primary"
            key="add"
            hidden={!access.hasPerms('system:user:add')}
            onClick={async () => {
              // const treeData = await getDeptTree({});
              // setDeptTree(treeData);

              // const postResp = await getPostList();
              // if (postResp.code === 200) {
              //   setPostList(
              //     postResp.rows.map((item: any) => {
              //       return {
              //         value: item.postId,
              //         label: item.postName,
              //       };
              //     }),
              //   );
              // }

              const {data: roleResp} = await getRoleList();
              setRoleList(
                roleResp.map((item: any) => {
                  return {
                    value: item.roleId,
                    label: item.roleName,
                  };
                }),
              );

              setCurrentRow(undefined);
              setModalVisible(true);
            }}
          >
            <PlusOutlined/>
            <FormattedMessage id="pages.searchTable.new" defaultMessage="新建"/>
          </Button>,
          <Button
            type="primary"
            key="remove"
            danger
            hidden={selectedRows?.length === 0 || !access.hasPerms('system:user:remove')}
            onClick={async () => {
              Modal.confirm({
                title: '是否确认删除所选数据项?',
                icon: <ExclamationCircleOutlined/>,
                content: '请谨慎操作',
                async onOk() {
                  const success = await handleRemove(selectedRows);
                  if (success) {
                    setSelectedRows([]);
                    actionRef.current?.reloadAndRest?.();
                  }
                },
                onCancel() {
                },
              });
            }}
          >
            <DeleteOutlined/>
            <FormattedMessage id="pages.searchTable.delete" defaultMessage="删除"/>
          </Button>,
          // <Button
          //   type="primary"
          //   key="export"
          //   hidden={!access.hasPerms('system:user:export')}
          //   onClick={async () => {
          //     handleExport(formTableRef);
          //   }}
          // >
          //   <DownloadOutlined />
          //   <FormattedMessage id="pages.searchTable.export" defaultMessage="导出" />
          // </Button>,
        ]}
        request={(params) =>
          getUserList({...params, deptId: selectDept.id} as API.System.UserListParams)
        }
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              <FormattedMessage id="pages.searchTable.chosen" defaultMessage="已选择"/>
              <a style={{fontWeight: 600}}>{selectedRows.length}</a>
              <FormattedMessage id="pages.searchTable.item" defaultMessage="项"/>
            </div>
          }
        >
          <Button
            key="remove"
            hidden={!access.hasPerms('system:user:del')}
            onClick={async () => {
              Modal.confirm({
                title: '删除',
                content: '确定删除该项吗？',
                okText: '确认',
                cancelText: '取消',
                onOk: async () => {
                  const success = await handleRemove(selectedRows);
                  if (success) {
                    setSelectedRows([]);
                    actionRef.current?.reloadAndRest?.();
                  }
                },
              });
            }}
          >
            <FormattedMessage id="pages.searchTable.batchDeletion" defaultMessage="批量删除"/>
          </Button>
        </FooterToolbar>
      )}
      <UpdateForm
        onSubmit={async (values) => {
          let success = false;
          if (values.userId) {
            success = await handleUpdate({...values} as API.System.User);
          } else {
            success = await handleAdd({...values} as API.System.User);
          }
          if (success) {
            setModalVisible(false);
            setCurrentRow(undefined);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
        onCancel={() => {
          setModalVisible(false);
          setCurrentRow(undefined);
        }}
        open={modalVisible}
        values={currentRow || {}}
        sexOptions={sexOptions}
        statusOptions={statusOptions}
        posts={postList || []}
        postIds={postIds || []}
        roles={roleList || []}
        roleIds={roleIds || []}
        depts={deptTree || []}
      />
      <ResetPwd
        onSubmit={async (values: any) => {
          const success = await resetUserPwd(values.userId, values.password);
          if (success) {
            setResetPwdModalVisible(false);
            setSelectedRows([]);
            setCurrentRow(undefined);
            message.success('密码重置成功。');
          }
        }}
        onCancel={() => {
          setResetPwdModalVisible(false);
          setSelectedRows([]);
          setCurrentRow(undefined);
        }}
        open={resetPwdModalVisible}
        values={currentRow || {}}
      />
      <AuthRoleForm
        onSubmit={async (values: any) => {
          console.log(currentRow);
          const success = await updateAuthRole({userId: currentRow?.userId, ...values});
          if (success) {
            setAuthRoleModalVisible(false);
            setSelectedRows([]);
            setCurrentRow(undefined);
            message.success('更新成功。');
          }
        }}
        onCancel={() => {
          setAuthRoleModalVisible(false);
          setSelectedRows([]);
          setCurrentRow(undefined);
        }}
        open={authRoleModalVisible}
        roles={roleList || []}
        roleIds={roleIds || []}
      />
    </PageContainer>
  );
};

export default UserTableList;
