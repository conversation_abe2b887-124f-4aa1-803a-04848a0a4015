import React, {useState} from 'react';
import {Form, List} from 'antd';
import {SecurityScanOutlined} from '@ant-design/icons';
import {ModalForm, ProFormText} from '@ant-design/pro-components';
import {FormattedMessage} from 'umi';
import {updateUserPwd} from '@/services/system/user';

type Unpacked<T> = T extends (infer U)[] ? U : T;

const passwordStrength = {
  strong: <span className="strong">强</span>,
  medium: <span className="medium">中</span>,
  weak: <span className="weak">弱 Weak</span>,
};


const SecurityView: React.FC = () => {
  const [form] = Form.useForm();
  const [upModalOpen, handleUpModalOpen] = useState<boolean>(false);


  const modifyPassward = () => {
    console.log('修改密码');
    handleUpModalOpen(true);
  };


  const checkPassword = (rule: any, value: string) => {
    const newPassword = form.getFieldValue('newPassword');
    if (value === newPassword) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('两次密码输入不一致'));
  };


  const getData = () => [
    {
      title: '账户密码',
      description: (
        <>
          当前密码强度：
          {passwordStrength.strong}
        </>
      ),
      actions: [<a key="Modify" onClick={modifyPassward}>修改</a>],
      avatar: <SecurityScanOutlined className='security-scan'/>,
    },
    // {
    //   title: '密保手机',
    //   description: `已绑定手机：138****8293`,
    //   actions: [<a key="Modify">修改</a>],
    // },
    // {
    //   title: '密保问题',
    //   description: '未设置密保问题，密保问题可有效保护账户安全',
    //   actions: [<a key="Set">设置</a>],
    // },
    // {
    //   title: '备用邮箱',
    //   description: `已绑定邮箱：ant***sign.com`,
    //   actions: [<a key="Modify">修改</a>],
    // },
    // {
    //   title: 'MFA 设备',
    //   description: '未绑定 MFA 设备，绑定后，可以进行二次确认',
    //   actions: [<a key="bind">绑定</a>],
    // },
  ];

  const data = getData();
  return (
    <>
      <List<Unpacked<typeof data>>
        itemLayout="horizontal"
        dataSource={data}
        renderItem={(item) => (
          <List.Item actions={item.actions}>
            <List.Item.Meta avatar={item.avatar} title={item.title} description={item.description}/>
          </List.Item>
        )}
      />

      <ModalForm
        labelCol={{span: 6}}
        grid={true}
        form={form}
        title="修改密码"
        width={520}
        layout="horizontal"
        open={upModalOpen}
        modalProps={{
          destroyOnClose: true,
          onCancel: () => handleUpModalOpen(false),
        }}
        onFinish={async (value) => {
          const success = await updateUserPwd(value.oldPassword, value.newPassword);
          if (success.code === 200) {
            handleUpModalOpen(false);
          }
        }}
      >
        <ProFormText.Password
          label="旧登录密码"
          rules={[
            {
              required: true,
              message: '请输入旧登录密码',
            },
          ]}
          allowClear={true}
          width="md"
          name="oldPassword"
        />
        <ProFormText.Password
          label="新登录密码"
          rules={[
            {
              required: true,
              message: <FormattedMessage id="请输入新密码！" defaultMessage="请输入新密码！"/>,
            },
          ]}
          allowClear={true}
          width="md"
          name="newPassword"
        />
        <ProFormText.Password
          label="确认登录密码"
          rules={[
            {
              required: true,
              message: (
                <FormattedMessage id="请输入确认密码！" defaultMessage="请输入确认密码！"/>
              ),
            },
            {validator: checkPassword},
          ]}
          allowClear={true}
          width="md"
          name="confirmPassword"
        />
      </ModalForm>
    </>
  );
};

export default SecurityView;
