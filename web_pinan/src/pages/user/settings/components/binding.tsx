// @ts-nocheck
import {AlipayOutlined, DingdingOutlined, MoneyCollectOutlined, TaobaoOutlined} from '@ant-design/icons';
import {ModalForm, ProFormText, ProFormTextArea} from '@ant-design/pro-components';
import {Form, List} from 'antd';
import React, {Fragment, useState} from 'react';
import {FormattedMessage, useModel} from 'umi';
import {getUser} from "@/services/system/user";

const BindingView: React.FC = () => {
  const [form] = Form.useForm();
  const [upModalOpen, handleUpModalOpen] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<any>();

  const {initialState} = useModel('@@initialState');
  const currentUser = initialState?.currentUser;


  const getData = () => [
    {
      title: '绑定淘宝',
      description: '当前未绑定淘宝账号',
      actions: [<a key="Bind">绑定</a>],
      avatar: <TaobaoOutlined className="taobao"/>,
    },
    {
      title: '绑定支付宝',
      description: '当前未绑定支付宝账号',
      actions: [<a key="Bind">绑定</a>],
      avatar: <AlipayOutlined className="alipay"/>,
    },
    {
      title: '绑定钉钉',
      description: '当前未绑定钉钉账号',
      actions: [<a key="Bind">绑定</a>],
      avatar: <DingdingOutlined className="dingding"/>,
    },
    {
      title: '钱包地址',
      description: '绑定钱包地址',
      actions: [<a key="moneyCollect" onClick={async () => {
        const res = await getUser(currentUser.userId)
        setCurrentRow(res.data.data);
        handleUpModalOpen(true);
      }}>绑定</a>],
      avatar: <MoneyCollectOutlined className="money-collect"/>,
    },
  ];

  return (
    <Fragment>
      <List
        itemLayout="horizontal"
        dataSource={getData()}
        renderItem={(item) => (
          <List.Item actions={item.actions}>
            <List.Item.Meta
              avatar={item.avatar}
              title={item.title}
              description={item.description}
            />
          </List.Item>
        )}
      />

      {/* 绑定地址 */}
      <ModalForm
        labelCol={{span: 5}}
        wrapperCol={{span: 18}}
        form={form}
        title="绑定地址"
        width="500px"
        layout="horizontal"
        open={upModalOpen}
        modalProps={{
          destroyOnClose: true,
          maskClosable: false,
          keyboard: false,
        }}
        initialValues={currentRow}
        onFinish={async (value) => {
          console.log(value);
          // const resp = await updateUserReceiveAddress(value);
          // if (resp.code === 200) {
          //   setCurrentRow({});
          //   handleUpModalOpen(false);
          //   history.go(0)
          // }
        }}
      >
        <ProFormText
          hidden
          name="userId"
        />
        <ProFormText
          label="用户名"
          rules={[
            {
              required: true,
              message: <FormattedMessage id="请输入用户名！" defaultMessage="请输入用户名！"/>,
            },
          ]}
          disabled
          allowClear={true}
          width="md"
          name="userName"
        />
        <ProFormTextArea
          label="钱包地址"
          rules={[
            {
              required: true,
              message: '请输入钱包地址',
            },
          ]}
          allowClear={true}
          width="xl"
          name="receiveAddress"
        />
      </ModalForm>

    </Fragment>
  );
};

export default BindingView;
