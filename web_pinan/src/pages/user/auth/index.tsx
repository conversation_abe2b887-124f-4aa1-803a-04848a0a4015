//@ts-nocheck
import {Helmet, history, useRequest} from '@umijs/max';
import {Button, Card, Col, Flex, Input, message, QRCode, Result, Row, Steps, Typography,} from 'antd';
import {useState} from 'react';
import {getGoogleAuthInfo} from '@/services/system/auth';

const {Paragraph} = Typography;

const Auth = () => {
  const [current, setCurrent] = useState(0);
  const [code, setCode] = useState('');

  const {data, loading} = useRequest(getGoogleAuthInfo, {initialData: {data: {}}});
  // const { run } = useRequest(setGoogleAuthInfo, { manual: true });

  const items = [
    {
      title: '第一步：创建秘钥',
      content: (
        <>
          <Flex vertical align="center" gap={20}>
            <QRCode
              id="bill_qr_code_url"
              value={data.url} //value参数为生成二维码的链接 我这里是由后端返回
              size={200} // 二维码的宽高尺寸
              fgColor="#000" //二维码的颜色
              bordered={false}
            />
            <Flex>
              安全秘钥：<Paragraph copyable>{data.googleAuthSecretKey}</Paragraph>
            </Flex>
            <span>打开手机谷歌验证器，并扫描上方二维码或手动输入安全秘钥</span>
          </Flex>
        </>
      ),
    },
    {
      title: '第二步：验证身份',
      content: (
        <>
          <Input
            placeholder="在此输入google验证器的验证码，并点击下一步完成绑定"
            width="500"
            onChange={(e) => {
              setCode(e.target.value);
            }}
          ></Input>
        </>
      ),
    },
    {
      title: '第三步：完成设置',
      content: (
        <Result
          status="success"
          title="绑定成功"
          extra={[
            <Button type="primary" key="console" onClick={() => history.push('/')}>
              返回首页
            </Button>,
          ]}
        />
      ),
    },
  ];

  const next = async () => {
    if (current === 1) {
      if (!code) {
        message.error('请输入验证码');
        return;
      }
      // const res = await setGoogleAuthInfo({ code, googleToken: data.googleAuthSecretKey });
      // if (res.code === 200) {
      //   message.success('绑定成功');
      //   (await getInitialState())?.fetchUserInfo();
      //   setCurrent(current + 1);
      // }
      return;
    }
    setCurrent(current + 1);
  };

  const prev = () => {
    setCurrent(current - 1);
  };

  return (
    <>
      <Helmet>
        <title>绑定谷歌验证码</title>
      </Helmet>
      <Card>
        <Steps current={current} items={items}></Steps>
        <Flex justify="center">
          <Row style={{width: 500}}>
            <Col span={24}>
              {!loading && <div style={{padding: '50px 0'}}>{items[current].content}</div>}
            </Col>
          </Row>
        </Flex>
        <Flex justify="center">
          {current > 0 && current < items.length - 1 && (
            <Button style={{margin: '0 8px'}} onClick={() => prev()}>
              上一步
            </Button>
          )}
          {current < items.length - 1 && (
            <Button type="primary" onClick={() => next()}>
              下一步
            </Button>
          )}
        </Flex>
      </Card>
    </>
  );
};
export default Auth;
