// @ts-nocheck
import Footer from './footer';
import { getCaptchaImg, login } from '@/services/system/auth';
import {
  AlipayCircleOutlined,
  LockOutlined,
  MobileOutlined,
  SafetyCertificateOutlined,
  TaobaoCircleOutlined,
  UserOutlined,
  WeiboCircleOutlined,
} from '@ant-design/icons';
import { LoginForm, ProFormCheckbox, ProFormText } from '@ant-design/pro-components';
import { useEmotionCss } from '@ant-design/use-emotion-css';
import { FormattedMessage, Helmet, history, useIntl, useModel, useRequest } from '@umijs/max';
import { Alert, Col, Image, message, Row, Tabs } from 'antd';
import Settings from '../../../../config/defaultSettings';
import React, { useEffect, useState } from 'react';
import { flushSync } from 'react-dom';
import { clearSessionToken, setSessionToken } from '@/access';
import { getSysConfig } from '@/services/system/config';
import qs from 'qs';

const ActionIcons = () => {
  const langClassName = useEmotionCss(({ token }) => {
    return {
      marginLeft: '8px',
      color: 'rgba(0, 0, 0, 0.2)',
      fontSize: '24px',
      verticalAlign: 'middle',
      cursor: 'pointer',
      transition: 'color 0.3s',
      '&:hover': {
        color: token.colorPrimaryActive,
      },
    };
  });

  return (
    <>
      <AlipayCircleOutlined key="AlipayCircleOutlined" className={langClassName} />
      <TaobaoCircleOutlined key="TaobaoCircleOutlined" className={langClassName} />
      <WeiboCircleOutlined key="WeiboCircleOutlined" className={langClassName} />
    </>
  );
};


const LoginMessage: React.FC<{
  content: string;
}> = ({content}) => {
  return (
    <Alert
      style={{
        marginBottom: 24,
      }}
      message={content}
      type="error"
      showIcon
    />
  );
};

const Login: React.FC = () => {
  const [userLoginState, setUserLoginState] = useState<API.LoginResult>({code: 200});
  const [type, setType] = useState<string>('account');
  const {initialState, setInitialState} = useModel('@@initialState');
  const [captchaCode, setCaptchaCode] = useState<string>('');
  const [uuid, setUuid] = useState<string>('');
  const [needCode, setNeedCode] = useState<boolean>(true);
  const containerClassName = useEmotionCss(() => {
    return {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage:
        "url('https://gw.alipayobjects.com/zos/rmsportal/TVYTbAXWheQpRcWDaDMu.svg')",
      backgroundSize: '100% 100%',
      paddingTop: 50,
    };
  });

  const intl = useIntl();

  const {data} = useRequest(getSysConfig, {
    initialData: {},
  });

  const getCaptchaCode = async () => {
    const {data: response} = await getCaptchaImg();
    setCaptchaCode(`data:image/png;base64,${response.img}`);
    setUuid(response.uuid);
    setNeedCode(response.captchaEnabled);
  };

  const fetchUserInfo = async () => {
    const userInfo = await initialState?.fetchUserInfo?.();
    if (userInfo) {
      flushSync(() => {
        setInitialState((s) => ({
          ...s,
          currentUser: userInfo,
        }));
      });
    }
  };

  const handleSubmit = async (values: API.LoginParams) => {
    try {
      // 登录
      const response = await login({...values, uuid}, {noTip: true});
      if (response.code === 200) {
        const defaultLoginSuccessMessage = intl.formatMessage({
          id: 'pages.login.success',
          defaultMessage: '登录成功！',
        });
        const current = new Date();
        const expireTime = current.setTime(current.getTime() + 1000 * 12 * 60 * 60);
        setSessionToken(response.data, response.data, expireTime);
        await fetchUserInfo();
        console.log('login ok');
        await message.success(defaultLoginSuccessMessage, 0.5).then(() => {
          const params = qs.parse(history.location.search, {ignoreQueryPrefix: true});
          history.push(params?.redirect || '/');
        });
      } else {
        console.log(response.msg);
        clearSessionToken();
        // 如果失败去设置用户错误信息
        setUserLoginState({...response, type});
        getCaptchaCode();
      }
    } catch (error) {
      getCaptchaCode();
      message.error(error.data.msg);
    }
  };
  const {code} = userLoginState;
  const loginType = type;

  useEffect(() => {
    getCaptchaCode();
  }, []);


  return (
    <div className={containerClassName}>
      <Helmet>
        <title>
          {`${intl.formatMessage({ id: 'menu.login', defaultMessage: '登录页' })}-${
            Settings.title
          }`}
        </title>
      </Helmet>
      <div
        style={{
          flex: '1',
          padding: '32px 0',
        }}
      >
        <LoginForm
          contentStyle={{
            minWidth: 280,
            maxWidth: '75vw',
          }}
          logo={<img alt="logo" src="/dk.jpg" />}
          title={`${data ? data['sys.name'] || '' : ''}`}
          // subTitle={data ? data['sys.subtitle'] || '' : ''}
          initialValues={{ autoLogin: true }}
          // actions={[
          //   <FormattedMessage
          //     key="loginWith"
          //     id="pages.login.loginWith"
          //     defaultMessage="其他登录方式"
          //   />,
          //   <ActionIcons key="icons"/>,
          // ]}
          onFinish={async (values) => {
            await handleSubmit(values as API.LoginParams);
          }}
        >
          <div style={{ marginTop: 30 }}></div>
          <Tabs
            activeKey={type}
            onChange={setType}
            centered
            items={[
              {
                key: 'account',
                label: intl.formatMessage({
                  id: 'pages.login.accountLogin.tab',
                  defaultMessage: '账户密码登录',
                }),
              },
              // {
              //   key: 'mobile',
              //   label: intl.formatMessage({
              //     id: 'pages.login.phoneLogin.tab',
              //     defaultMessage: '手机号登录',
              //   }),
              // },
            ]}
          />

          {code !== 200 && loginType === 'account' && (
            <LoginMessage
              content={intl.formatMessage({
                id: 'pages.login.accountLogin.errorMessage',
                defaultMessage: '账户或密码错误',
              })}
            />
          )}
          {type === 'account' && (
            <div>
              <ProFormText
                name="username"
                fieldProps={{
                  size: 'large',
                  prefix: <UserOutlined />,
                }}
                placeholder="用户名"
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage
                        id="pages.login.username.required"
                        defaultMessage="请输入用户名!"
                      />
                    ),
                  },
                ]}
              />
              <ProFormText.Password
                name="password"
                fieldProps={{
                  size: 'large',
                  prefix: <LockOutlined />,
                }}
                placeholder="密码"
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage
                        id="pages.login.password.required"
                        defaultMessage="请输入密码！"
                      />
                    ),
                  },
                ]}
              />
              {needCode && (
                <Row>
                  <Col flex={3}>
                    <ProFormText
                      style={{
                        float: 'right',
                      }}
                      name="code"
                      placeholder={intl.formatMessage({
                        id: 'pages.login.captcha.placeholder',
                        defaultMessage: '请输入验证啊',
                      })}
                      fieldProps={{
                        prefix: <SafetyCertificateOutlined />,
                      }}
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage
                              id="pages.searchTable.updateForm.ruleName.nameRules"
                              defaultMessage="请输入验证啊"
                            />
                          ),
                        },
                      ]}
                    />
                  </Col>
                  <Col flex={2}>
                    <Image
                      src={captchaCode}
                      alt="验证码"
                      style={{
                        display: 'inline-block',
                        verticalAlign: 'top',
                        cursor: 'pointer',
                        paddingLeft: '10px',
                        width: '100px',
                      }}
                      preview={false}
                      onClick={() => getCaptchaCode()}
                    />
                  </Col>
                </Row>
              )}
            </div>
          )}

          {code !== 200 && loginType === 'mobile' && <LoginMessage content="验证码错误" />}
          {type === 'mobile' && (
            <>
              <ProFormText
                fieldProps={{
                  size: 'large',
                  prefix: <MobileOutlined />,
                }}
                name="mobile"
                placeholder={intl.formatMessage({
                  id: 'pages.login.phoneNumber.placeholder',
                  defaultMessage: '手机号',
                })}
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage
                        id="pages.login.phoneNumber.required"
                        defaultMessage="请输入手机号！"
                      />
                    ),
                  },
                  {
                    pattern: /^1\d{10}$/,
                    message: (
                      <FormattedMessage
                        id="pages.login.phoneNumber.invalid"
                        defaultMessage="手机号格式错误！"
                      />
                    ),
                  },
                ]}
              />
              {/*<ProFormCaptcha*/}
              {/*  fieldProps={{*/}
              {/*    size: 'large',*/}
              {/*    prefix: <LockOutlined />,*/}
              {/*  }}*/}
              {/*  captchaProps={{*/}
              {/*    size: 'large',*/}
              {/*  }}*/}
              {/*  placeholder={intl.formatMessage({*/}
              {/*    id: 'pages.login.captcha.placeholder',*/}
              {/*    defaultMessage: '请输入验证码',*/}
              {/*  })}*/}
              {/*  captchaTextRender={(timing, count) => {*/}
              {/*    if (timing) {*/}
              {/*      return `${count} ${intl.formatMessage({*/}
              {/*        id: 'pages.getCaptchaSecondText',*/}
              {/*        defaultMessage: '获取验证码',*/}
              {/*      })}`;*/}
              {/*    }*/}
              {/*    return intl.formatMessage({*/}
              {/*      id: 'pages.login.phoneLogin.getVerificationCode',*/}
              {/*      defaultMessage: '获取验证码',*/}
              {/*    });*/}
              {/*  }}*/}
              {/*  name="captcha"*/}
              {/*  rules={[*/}
              {/*    {*/}
              {/*      required: true,*/}
              {/*      message: (*/}
              {/*        <FormattedMessage*/}
              {/*          id="pages.login.captcha.required"*/}
              {/*          defaultMessage="请输入验证码！"*/}
              {/*        />*/}
              {/*      ),*/}
              {/*    },*/}
              {/*  ]}*/}
              {/*  onGetCaptcha={async (phone) => {*/}
              {/*    const result = await getFakeCaptcha({*/}
              {/*      phone,*/}
              {/*    });*/}
              {/*    if (!result) {*/}
              {/*      return;*/}
              {/*    }*/}
              {/*    message.success('获取验证码成功！验证码为：1234');*/}
              {/*  }}*/}
              {/*/>*/}
            </>
          )}
          <div
            style={{
              marginBottom: 24,
            }}
          >
            <ProFormCheckbox noStyle name="autoLogin">
              <FormattedMessage id="pages.login.rememberMe" defaultMessage="自动登录" />
            </ProFormCheckbox>
            <a
              style={{
                float: 'right',
              }}
            >
              <FormattedMessage id="pages.login.forgotPassword" defaultMessage="忘记密码" />
            </a>
          </div>
        </LoginForm>
      </div>
      <Footer />
    </div>
  );
};

export default Login;
