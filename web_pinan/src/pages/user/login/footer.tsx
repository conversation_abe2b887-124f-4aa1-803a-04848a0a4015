// @ts-nocheck
import {getSysConfig} from '@/services/system/config';
import {useRequest} from '@umijs/max';
import React, {useEffect} from 'react';
import {Flex, theme} from 'antd';

const {useToken} = theme;

const Footer: React.FC = () => {
  const {data} = useRequest(getSysConfig, {
    initialData: {},
  });
  const {token} = useToken();

  // useEffect(() => {
  //   // Dynamically modify the browser icon
  //   if (data['sys.app.tabIco']) {
  //     const link = document.createElement('link');
  //     link.type = 'image/x-icon';
  //     link.rel = 'shortcut icon';
  //     link.href = data['sys.app.tabIco'];
  //     document.querySelector('head')?.appendChild(link);
  //   }
  // }, [data]);

  return (
    <footer
      style={{
        marginBlock: 0,
        marginBlockStart: '24px',
        marginBlockEnd: '24px',
        marginInline: 0,
        marginInlineStart: '0px',
        marginInlineEnd: '0px',
        paddingBlock: 0,
        paddingInline: '16px',
        textAlign: 'center',
        color: token.colorText,
      }}
    >
      <Flex gap="small" align="center" justify="center">
        <span style={{cursor: 'pointer'}}>{data ? data['sys.copyright'] || '' : ''}</span>
      </Flex>
    </footer>
  );
};

export default Footer;
