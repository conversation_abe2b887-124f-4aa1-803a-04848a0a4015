// @ts-nocheck
import {CheckOutlined, CopyOutlined, DownloadOutlined, PlusOutlined} from '@ant-design/icons';
import type {FormInstance} from 'antd';
import {Button, Card, Drawer, message, Modal,} from 'antd';
import React, {useRef, useState} from 'react';
import {FormattedMessage, history, useAccess} from '@umijs/max';
import type {GenCodeTableListParams, GenCodeType} from './data.d';
import {batchGenCode, genCode, getGenCodeList, previewCode, removeData, syncDbInfo,} from './service';
import {
  ActionType,
  FooterToolbar,
  PageContainer,
  ProColumns,
  ProDescriptions,
  ProDescriptionsItemProps,
  ProTable,
} from '@ant-design/pro-components';
import {copy} from "@/utils/utils";
import SyntaxHighlighter from 'react-syntax-highlighter';
import {githubGist} from 'react-syntax-highlighter/dist/esm/styles/hljs';


/**
 * 删除节点
 *
 * @param selectedRows
 */
const handleRemove = async (selectedRows: GenCodeType[]) => {
  const hide = message.loading('正在删除');
  if (!selectedRows) return true;
  try {
    await removeData({
      ids: selectedRows.map((row) => row.tableId),
    });
    hide();
    message.success('删除成功');
    return true;
  } catch (error) {
    hide();
    //  message.error('删除失败，请重试');
    return false;
  }
};

const handleRemoveOne = async (selectedRow: GenCodeType) => {
  const hide = message.loading('正在删除');
  if (!selectedRow) return true;
  try {
    const params = [selectedRow.tableId];
    await removeData({
      ids: params,
    });
    hide();
    message.success('删除成功');
    return true;
  } catch (error) {
    hide();
    //  message.error('删除失败，请重试');
    return false;
  }
};

const GenCodeView: React.FC = () => {
  const formTableRef = useRef<FormInstance>();


  const [showPreview, setShowPreview] = useState<boolean>(false);
  const [previewData, setPreivewData] = useState<boolean>(false);
  const [copySuccess, setCopySuccess] = useState<boolean>(false);

  const [previewLoading, setpreviewLoading] = useState<boolean>(true);

  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<GenCodeType>();
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [currentPreviewLanguage, setCurrentPreviewLanguage] = useState<string>('java');
  const [selectedRows, setSelectedRows] = useState<GenCodeType[]>([]);
  const [currentPreviewKey, setCurrentPreviewKey] = useState<string>('vm/java/domain.java.vm');

  const access = useAccess();

  const columns: ProColumns<GenCodeType>[] = [
    {
      title: '编号',
      dataIndex: 'tableId',
      tip: '编号',
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '表名',
      dataIndex: 'tableName',
      valueType: 'textarea',
    },
    {
      title: '表描述',
      dataIndex: 'tableComment',
      hideInForm: true,
      hideInSearch: true,
    },
    {
      title: '实体',
      dataIndex: 'className',
      valueType: 'textarea',
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'textarea',
      hideInSearch: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: 'textarea',
      hideInSearch: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      width: '220px',
      valueType: 'option',
      render: (_, record) => [
        <Button
          type="link"
          size="small"
          key="preview"
          hidden={!access.hasPerms('tool:gen:edit')}
          onClick={() => {
            setpreviewLoading(true);
            previewCode(record.tableId).then((res) => {
              setpreviewLoading(false);
              if (res.code === 200) {
                setPreivewData(res.data);
                setShowPreview(true);
              }
            });
          }}
        >
          预览
        </Button>,
        <Button
          type="link"
          size="small"
          key="config"
          hidden={!access.hasPerms('tool:gen:edit')}
          onClick={() => {
            history.push(`/tool/gen/edit?id=${record.tableId}`);
          }}
        >
          编辑
        </Button>,
        <Button
          type="link"
          size="small"
          danger
          key="delete"
          hidden={!access.hasPerms('tool:gen:del')}
          onClick={async () => {
            Modal.confirm({
              title: '删除任务',
              content: '确定删除该任务吗？',
              okText: '确认',
              cancelText: '取消',
              onOk: async () => {
                const success = await handleRemoveOne(record);
                if (success) {
                  if (actionRef.current) {
                    actionRef.current.reload();
                  }
                }
              },
            });
          }}
        >
          删除
        </Button>,
        <Button
          type="link"
          size="small"
          key="sync"
          hidden={!access.hasPerms('tool:gen:edit')}
          onClick={() => {
            syncDbInfo(record.tableName).then((res) => {
              if (res.code === 200) {
                message.success('同步成功');
              } else {
                message.error('同步失败');
              }
            });
          }}
        >
          同步
        </Button>,
        <Button
          type="link"
          size="small"
          key="gencode"
          hidden={!access.hasPerms('tool:gen:edit')}
          onClick={() => {
            if (record.genType === '1') {
              genCode(record.tableName).then((res) => {
                if (res.code === 200) {
                  message.success(`成功生成到自定义路径：${record.genPath}`);
                } else {
                  message.error(res.msg);
                }
              });
            } else {
              batchGenCode(record.tableName);
            }
          }}
        >
          生成代码
        </Button>,
      ],
    },
  ];


  const tabList = [
    {key: 'vm/java/domain.java.vm', tab: 'domain.java',},
    {key: 'vm/java/convert.java.vm', tab: 'convert.java'},
    {key: 'vm/java/postVo.java.vm', tab: 'postVo.java'},
    {key: 'vm/java/putVo.java.vm', tab: 'putVo.java'},

    {key: 'vm/java/mapper.java.vm', tab: 'mapper.java'},
    {key: 'vm/java/service.java.vm', tab: 'service.java'},
    {key: 'vm/java/serviceImpl.java.vm', tab: 'serviceImpl.java'},
    {key: 'vm/java/controller.java.vm', tab: 'controller.java'},

    {key: 'vm/xml/mapper.xml.vm', tab: 'mapper.xml'},
    {key: 'vm/sql/sql.vm', tab: 'sql'},

    {key: 'antdpro/Form1.tsx.vm', tab: 'edit.tsx'},
    {key: 'antdpro/index1.tsx.vm', tab: 'index.tsx'},
    {key: 'antdpro/service1.ts.vm', tab: 'api.ts'},
    {key: 'antdpro/data1.d.ts.vm', tab: 'data.d.ts'},
    {key: 'antdpro/locale1.ts.vm', tab: 'locale.ts'},
    // {key: 'antdpro/_mock1.ts.vm', tab: '_mock.ts'},
  ];


  return (
    <PageContainer>
      <ProTable<GenCodeType>
        headerTitle="代码生成信息"
        actionRef={actionRef}
        formRef={formTableRef}
        rowKey="tableId"
        toolBarRender={() => [
          <Button
            type="primary"
            key="gen"
            hidden={!access.hasPerms('tool:gen:edit')}
            onClick={() => {
              if (selectedRows.length === 0) {
                message.error('请选择要生成的数据');
                return;
              }
              const tableNames = selectedRows.map((row) => row.tableName);
              if (selectedRows[0].genType === '1') {
                genCode(tableNames.join(',')).then((res) => {
                  if (res.code === 200) {
                    message.success(`成功生成到自定义路径：${selectedRows[0].genPath}`);
                  } else {
                    message.error(res.msg);
                  }
                });
              } else {
                batchGenCode(tableNames.join(','));
              }
            }}
          >
            <DownloadOutlined/> <FormattedMessage id="gen.gencode" defaultMessage="生成"/>
          </Button>,
          <Button
            type="primary"
            key="import"
            hidden={!access.hasPerms('tool:gen:add')}
            onClick={() => {
              history.push('/tool/gen/import');
            }}
          >
            <PlusOutlined/> <FormattedMessage id="gen.import" defaultMessage="导入"/>
          </Button>,
        ]}
        request={(params) =>
          getGenCodeList({...params} as GenCodeTableListParams).then((res) => {
            return {
              data: res.data,
              total: res.total,
              success: true,
            };
          })
        }
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              <FormattedMessage id="pages.searchTable.chosen" defaultMessage="已选择"/>{' '}
              <a style={{fontWeight: 600}}>{selectedRows.length}</a>{' '}
              <FormattedMessage id="pages.searchTable.item" defaultMessage="项"/>
            </div>
          }
        >
          <Button
            key="delete"
            hidden={!access.hasPerms('tool:gen:remove')}
            onClick={async () => {
              Modal.confirm({
                title: '删除任务',
                content: '确定删除该任务吗？',
                okText: '确认',
                cancelText: '取消',
                onOk: async () => {
                  const success = await handleRemove(selectedRows);
                  if (success) {
                    setSelectedRows([]);
                    actionRef.current?.reloadAndRest?.();
                  }
                },
              });
            }}
          >
            <FormattedMessage id="pages.searchTable.batchDeletion" defaultMessage="批量删除"/>
          </Button>
        </FooterToolbar>
      )}

      {/* 预览代码 */}
      <Drawer
        open={showPreview}
        title={'生成代码预览'}
        onClose={() => {
          setShowPreview(false);
        }}
        destroyOnClose
        width={'80%'}
        extra={
          <Button
            type="link"
            disabled={previewLoading}
            onClick={async () => {
              copy(previewData[currentPreviewKey], () => {
                setCopySuccess(true);
                setTimeout(() => {
                  setCopySuccess(false);
                }, 1500);
              });
            }}
          >
            {copySuccess ? (
              <>
                <CheckOutlined/> 复制成功
              </>
            ) : (
              <>
                <CopyOutlined/> 复制
              </>
            )}
          </Button>
        }
      >
        <Card
          title={false}
          loading={previewLoading}
          tabList={tabList}
          bordered={false}
          onTabChange={(key) => {
            setCurrentPreviewKey(key);
            if (key.indexOf('java.vm') > -1) {
              setCurrentPreviewLanguage('java');
            } else if (key.indexOf('xml.vm') > -1) {
              setCurrentPreviewLanguage('xml');
            } else if (key.indexOf('sql.vm') > -1) {
              setCurrentPreviewLanguage('sql');
            } else if (key.indexOf('ts.vm') > -1) {
              setCurrentPreviewLanguage('ts');
            } else if (key.indexOf('tsx.vm') > -1) {
              setCurrentPreviewLanguage('tsx');
            }
          }}
        >
          {/* @ts-ignore */}
          <SyntaxHighlighter showLineNumbers style={githubGist} language={currentPreviewLanguage}>
            {previewData[currentPreviewKey]}
          </SyntaxHighlighter>
        </Card>
      </Drawer>

      {/* 抽屉循环遍历字段 */}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.tableName && (
          <ProDescriptions<GenCodeType>
            column={2}
            title={currentRow?.tableName}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.tableName,
            }}
            columns={columns as ProDescriptionsItemProps<GenCodeType>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default GenCodeView;
