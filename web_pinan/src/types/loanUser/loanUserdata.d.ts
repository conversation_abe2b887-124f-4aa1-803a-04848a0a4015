import {BaseEntity} from "@/types/typings";

/**
 * 贷款用户 biz_loan_user
 *
 * @date 2025-08-11
 */
export interface LoanUser extends BaseEntity {
  // 用户ID
  id: string;
  // 用户账号
  userName: string;
  // 密码
  password: string;
  // 手机号码
  phoneNumber: string;
  // 身份证号码
  idCard: string;
  // 开户银行
  openBank: string;
  // 银行卡号
  bankNumber: string;
  // 开户地址
  openAddress: string;
  // 显示顺序
  sort: string;
  // 帐号状态（1正常 2停用）
  status: string;
  // 用户类型（00系统用户，1.商户）
  userType: string;
  // 最后登录IP
  loginIp: string;
  // 最后登录时间
  loginDate: string;
}
