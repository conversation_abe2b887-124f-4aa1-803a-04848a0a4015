// @ts-nocheck
import {downLoadXlsx} from '@/utils/downloadfile';
import {request} from '@umijs/max';

// 查询参数配置列表
export async function getConfigList(params?: API.System.ConfigListParams) {
  return request<API.System.ConfigPageResult>('/api/system/config/list', {
    method: 'GET',
    params,
  });
}

// 查询参数配置详细
export function getConfig(configId: number) {
  return request<API.System.ConfigInfoResult>(`/api/config/${configId}`, {
    method: 'GET',
  });
}

// 查询参数配置详细byKey
export function getConfigByKey(key: string) {
  return request<API.System.ConfigInfoResult>(`/api/configKey/${key}`, {
    method: 'GET',
  });
}

export function getSysConfig() {
  return request<API.System.ConfigInfoResult>(`/api/nosign/config`, {
    method: 'GET',
  });
}

// 新增参数配置
export async function addConfig(params: API.System.Config) {
  return request<API.Result>('/api/system/config', {
    method: 'POST',
    data: params,
  });
}

// 修改参数配置
export async function updateConfig(params: API.System.Config) {
  return request<API.Result>('/api/system/config', {
    method: 'PUT',
    data: params,
  });
}

// 删除参数配置
export async function removeConfig(ids: string) {
  return request<API.Result>(`/api/system/config/${ids}`, {
    method: 'DELETE',
  });
}

// 导出参数配置
export function exportConfig(params?: API.System.ConfigListParams, name: string) {
  // return request<API.Result>(`/api/config/export`, {
  //   method: 'GET',
  //   params
  // });
  return downLoadXlsx(`/api/system/config/export`, params, name);
}

// 刷新参数缓存
export function refreshConfigCache() {
  return request<API.Result>('/api/system/config/refreshCache', {
    method: 'delete',
  });
}
