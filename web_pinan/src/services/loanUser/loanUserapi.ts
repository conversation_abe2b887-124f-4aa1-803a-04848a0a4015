// @ts-nocheck
import {downLoadXlsx} from '@/utils/downloadfile';
import {request} from '@umijs/max';
import type {LoanUser} from '@/types/loanUser/loanUserdata.d';
import {paramsSortable} from "@/utils/utils";

/**
 * SERVICE: 贷款用户
 *
 * @date 2025-08-11
 */
// 新增收款信息
export const addReceivePayInfo = (params: ReceivePayInfo) => {
  return request('/api/biz/payInfo', {
    method: 'POST',
    data: params,
  });
};

// 修改收款信息
export const updateReceivePayInfo = (params: ReceivePayInfo) => {
  return request(`/api/biz/payInfo/${params.id}`, {
    method: 'PUT',
    data: params,
  });
};

// 新增贷款信息
export const addLoanInfo = (params: LoanInfo) => {
  return request('/api/biz/loanInfo', {
    method: 'POST',
    data: params,
  });
};

// 修改贷款信息
export const updateLoanInfo = (params: LoanInfo) => {
  return request(`/api/biz/loanInfo/${params.id}`, {
    method: 'PUT',
    data: params,
  });
};

// 查询贷款用户列表
export const getLoanUserList = (params?: LoanUser, sort: any) => {
  return request('/api/biz/loanUser/list', {
    method: 'GET',
    params: paramsSortable(params, sort),
  }).then((res) => {
    return {
      data: res.data,
      total: res.total,
      success: true,
    };
  });
};

// 获取贷款用户详细信息
export const getLoanUser = (id: string) => {
  return request(`/api/biz/loanUser/${id}`, {
    method: 'GET',
  });
};

// 新增贷款用户
export const addLoanUser = (params: LoanUser) => {
  return request('/api/biz/loanUser', {
    method: 'POST',
    data: params,
  });
};

// 修改贷款用户
export const updateLoanUser = (params: LoanUser) => {
  return request(`/api/biz/loanUser/${params.id}`, {
    method: 'PUT',
    data: params,
  });
};

// 删除贷款用户
export const removeLoanUser = (ids: string) => {
  return request(`/api/biz/loanUser/${ids}`, {
    method: 'DELETE',
  });
};

// 导出贷款用户列表
export const exportLoanUser = (params?: LoanUser) => {
  return downLoadXlsx(
    `/api/biz/loanUser/export`,
    { params },
    `biz_loanUser_$#{Number(new Date())}.xlsx`,
  );
};
