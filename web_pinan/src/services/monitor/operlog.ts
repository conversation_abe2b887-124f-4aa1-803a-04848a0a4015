// @ts-nocheck
import {downLoadXlsx} from '@/utils/downloadfile';
import {request} from '@umijs/max';

// 查询操作日志记录列表
export async function getOperlogList(params?: API.Monitor.OperlogListParams) {
  return request<API.Monitor.OperlogPageResult>('/api/monitor/operlog/list', {
    method: 'GET',
    params,
  });
}

// 查询操作日志记录详细
export function getOperlog(operId: number) {
  return request<API.Monitor.OperlogInfoResult>(`/api/monitor/operlog/${operId}`, {
    method: 'GET',
  });
}

// 新增操作日志记录
export async function addOperlog(params: API.Monitor.Operlog) {
  return request<API.Result>('/api/monitor/operlog', {
    method: 'POST',
    data: params,
  });
}

// 修改操作日志记录
export async function updateOperlog(params: API.Monitor.Operlog) {
  return request<API.Result>('/api/monitor/operlog', {
    method: 'PUT',
    data: params,
  });
}

// 删除操作日志记录
export async function removeOperlog(ids: string) {
  return request<API.Result>(`/api/monitor/operlog/${ids}`, {
    method: 'DELETE',
  });
}

// 导出操作日志记录
export function exportOperlog(params?: API.Monitor.OperlogListParams, name) {
  // return request<API.Result>(`/api/monitor/operlog/export`, {
  //   method: 'GET',
  //   params
  // });
  return downLoadXlsx(`/api/monitor/operlog/export`, params, name);
}
