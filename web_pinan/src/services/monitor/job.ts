// @ts-nocheck
import {downLoadXlsx} from '@/utils/downloadfile';
import {request} from '@umijs/max';

/**
 * 定时任务调度 API
 *
 * <AUTHOR>
 * @date 2023-02-07
 */

// 查询定时任务调度列表
export async function getJobList(params?: API.Monitor.JobListParams) {
  return request<API.Monitor.JobPageResult>('/api/monitor/job/list', {
    method: 'GET',
    params,
  });
}

// 查询定时任务调度详细
export function getJob(jobId: number) {
  return request<API.Monitor.JobInfoResult>(`/api/monitor/job/${jobId}`, {
    method: 'GET',
  });
}

// 新增定时任务调度
export async function addJob(params: API.Monitor.Job) {
  return request<API.Result>('/api/monitor/job', {
    method: 'POST',
    data: params,
  });
}

// 修改定时任务调度
export async function updateJob(params: API.Monitor.Job) {
  return request<API.Result>('/api/monitor/job', {
    method: 'PUT',
    data: params,
  });
}

// 删除定时任务调度
export async function removeJob(ids: string) {
  return request<API.Result>(`/api/monitor/job/${ids}`, {
    method: 'DELETE',
  });
}

// 导出定时任务调度
export function exportJob(params?: API.Monitor.JobListParams, name: string) {
  // return request<API.Result>(`/api/monitor/job/export`, {
  //   method: 'GET',
  //   params
  // });
  return downLoadXlsx(`/api/monitor/job/export`, params, name);
}

// 定时任务立即执行一次
export async function runJob(jobId: number, jobGroup: string) {
  const job = {
    jobId,
    jobGroup,
  };
  return request('/api/monitor/job/run', {
    method: 'PUT',
    data: job,
  });
}
