import React, { useEffect, useState } from "react";

const DownloadApp: React.FC = () => {
  const [isMobile, setIsMobile] = useState<boolean>(true); // 是否显示二维码和按钮


  return (
    <div style={{ textAlign: "center", marginTop: "50px", fontFamily: "Arial, sans-serif" }}>
      <h1>请扫描二维码下载</h1>
      <p>拍拍借</p>
        <>
          <img
            src="/nwq386ba.com.png"
            alt="下载二维码"
            style={{ width: "200px", height: "200px", marginTop: "20px" }}
          />
          <br />
        </>
    </div>
  );
};

export default DownloadApp;
