# admin_pinan 金融贷款管理系统 Docker 部署指南

## 概述

本指南将帮助您在 Windows Docker Desktop + WSL2 环境中部署 admin_pinan 金融贷款管理系统。

## 系统要求

### 硬件要求
- CPU: 4核心或以上
- 内存: 8GB 或以上
- 磁盘空间: 20GB 可用空间

### 软件要求
- Windows 10/11 (版本 2004 或更高)
- Docker Desktop for Windows (最新版本)
- WSL2 (Windows Subsystem for Linux 2)
- Git for Windows

## 环境准备

### 1. 安装 Docker Desktop

1. 下载并安装 [Docker Desktop for Windows](https://www.docker.com/products/docker-desktop)
2. 启动 Docker Desktop
3. 在设置中启用 WSL2 集成

### 2. 配置 WSL2

```bash
# 在 PowerShell 中以管理员身份运行
wsl --install
wsl --set-default-version 2

# 安装 Ubuntu 发行版
wsl --install -d Ubuntu
```

### 3. 验证环境

```bash
# 在 WSL2 终端中验证
docker --version
docker-compose --version
```

## 项目结构

```
admin_pinan/
├── docker-compose.yml              # Docker Compose 配置文件
├── admin_pinan/                     # 后端项目
│   ├── admin/                       # Web 入口模块
│   ├── framework/                   # 框架模块
│   ├── system/                      # 系统模块
│   ├── biz/                         # 业务模块
│   ├── common/                      # 通用模块
│   ├── generator/                   # 代码生成模块
│   ├── quartz/                      # 定时任务模块
│   └── sql/                         # 数据库脚本
├── web_pinan/                       # 前端项目
├── docker/                          # Docker 配置文件
│   ├── backend/                     # 后端 Dockerfile
│   ├── frontend/                    # 前端 Dockerfile
│   ├── nginx/                       # Nginx 配置
│   ├── mysql/                       # MySQL 配置
│   └── redis/                       # Redis 配置
├── scripts/                         # 部署脚本
│   ├── start.sh                     # 启动脚本
│   ├── stop.sh                      # 停止脚本
│   └── logs.sh                      # 日志查看脚本
└── logs/                            # 日志目录
```

## 快速部署

### 1. 克隆项目

```bash
# 在 WSL2 终端中执行
git clone <repository-url>
cd admin_pinan
```

### 2. 一键启动

```bash
# 给脚本执行权限
chmod +x scripts/*.sh

# 启动系统
./scripts/start.sh
```

### 3. 访问系统

- **前端管理界面**: http://localhost
- **后端 API**: http://localhost:8088
- **Druid 监控**: http://localhost:8088/druid

### 4. 默认账号

- **管理员账号**: admin / admin123
- **Druid 监控**: admin / admin123

## 手动部署步骤

### 1. 构建镜像

```bash
# 构建所有镜像
docker-compose build

# 或分别构建
docker-compose build backend
docker-compose build frontend
```

### 2. 启动服务

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 3. 查看日志

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f mysql
docker-compose logs -f redis
```

## 服务配置

### MySQL 配置

- **端口**: 3306
- **数据库**: pinandai
- **用户名**: pinan
- **密码**: pinan123
- **Root 密码**: root123456

### Redis 配置

- **端口**: 6379
- **数据库**: 2 (后端使用)
- **无密码认证**

### 后端配置

- **端口**: 8088
- **配置文件**: application-docker.yml
- **日志目录**: /app/logs
- **上传目录**: /app/upload

### 前端配置

- **端口**: 80
- **Nginx 配置**: docker/nginx/
- **API 代理**: /api/* -> http://backend:8088/

## 常用操作

### 查看服务状态

```bash
docker-compose ps
```

### 重启服务

```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart backend
```

### 停止服务

```bash
# 停止所有服务
docker-compose down

# 停止并删除数据卷
docker-compose down -v
```

### 进入容器

```bash
# 进入后端容器
docker-compose exec backend bash

# 进入 MySQL 容器
docker-compose exec mysql mysql -u pinan -p pinandai

# 进入 Redis 容器
docker-compose exec redis redis-cli
```

### 查看日志

```bash
# 使用脚本查看日志
./scripts/logs.sh backend -f
./scripts/logs.sh mysql -n 100
./scripts/logs.sh all --follow
```

## 故障排除

### 1. 端口冲突

如果遇到端口冲突，修改 docker-compose.yml 中的端口映射：

```yaml
services:
  frontend:
    ports:
      - "8080:80"  # 将前端端口改为 8080
  backend:
    ports:
      - "8089:8088"  # 将后端端口改为 8089
```

### 2. 内存不足

增加 Docker Desktop 的内存限制：
1. 打开 Docker Desktop
2. 进入 Settings -> Resources
3. 增加 Memory 限制到 4GB 或更高

### 3. 数据库连接失败

检查 MySQL 服务状态：

```bash
# 查看 MySQL 日志
docker-compose logs mysql

# 测试数据库连接
docker-compose exec mysql mysql -u pinan -p pinandai
```

### 4. 前端无法访问后端

检查网络连接：

```bash
# 查看网络配置
docker network ls
docker network inspect admin_pinan_pinan_network

# 测试后端服务
curl http://localhost:8088/actuator/health
```

### 5. 构建失败

清理 Docker 缓存：

```bash
# 清理构建缓存
docker system prune -a

# 重新构建
docker-compose build --no-cache
```

## 数据备份与恢复

### 备份数据

```bash
# 备份 MySQL 数据
docker-compose exec mysql mysqldump -u pinan -p pinandai > backup.sql

# 备份 Redis 数据
docker-compose exec redis redis-cli BGSAVE
```

### 恢复数据

```bash
# 恢复 MySQL 数据
docker-compose exec -T mysql mysql -u pinan -p pinandai < backup.sql
```

## 性能优化

### 1. JVM 参数调优

修改 docker/backend/Dockerfile 中的 JVM 参数：

```dockerfile
ENTRYPOINT ["java", \
    "-Xms1024m", \
    "-Xmx2048m", \
    "-XX:+UseG1GC", \
    "-jar", "app.jar"]
```

### 2. MySQL 参数调优

修改 docker/mysql/conf/my.cnf：

```ini
[mysqld]
innodb_buffer_pool_size=512M
max_connections=500
```

### 3. Redis 参数调优

修改 docker/redis/redis.conf：

```conf
maxmemory 512mb
maxmemory-policy allkeys-lru
```

## 安全配置

### 1. 修改默认密码

在生产环境中，请修改以下默认密码：

- MySQL root 密码
- MySQL 用户密码
- Redis 密码（如需要）
- 应用管理员密码

### 2. 网络安全

```yaml
# 在 docker-compose.yml 中限制端口暴露
services:
  mysql:
    ports:
      - "127.0.0.1:3306:3306"  # 只绑定本地接口
```

## 监控与日志

### 1. 应用监控

- **健康检查**: http://localhost:8088/actuator/health
- **应用信息**: http://localhost:8088/actuator/info
- **Druid 监控**: http://localhost:8088/druid

### 2. 日志管理

日志文件位置：
- 后端日志: logs/backend/
- Nginx 日志: logs/nginx/
- MySQL 日志: docker/mysql/logs/
- Redis 日志: docker/redis/logs/

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看相关服务的日志
2. 检查 Docker 和系统资源
3. 参考故障排除章节
4. 联系技术支持团队
