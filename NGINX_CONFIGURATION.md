# Nginx 配置管理指南

## 概述

admin_pinan 项目采用了灵活的 Nginx 配置管理方案，支持多环境配置切换和外部配置文件管理。

## 配置架构

### 1. 配置文件结构

```
docker/nginx/
├── nginx.conf                  # Nginx 主配置文件
├── conf.d/
│   ├── default.conf            # 当前使用的配置（默认）
│   ├── default.dev.conf        # 开发环境配置
│   └── default.prod.conf       # 生产环境配置
```

### 2. 配置管理方式

- **外部配置文件挂载**: 通过 Docker 卷挂载方式管理配置
- **环境特定配置**: 支持开发、生产等不同环境的配置
- **动态切换**: 无需重新构建镜像即可切换配置
- **配置验证**: 自动验证配置文件语法正确性

## 配置环境说明

### 1. 默认配置 (default.conf)

**适用场景**: 通用部署环境
**特点**:
- 平衡的安全设置
- 适中的缓存策略
- 标准的代理配置

### 2. 开发环境配置 (default.dev.conf)

**适用场景**: 本地开发和测试
**特点**:
- 详细的调试日志
- 禁用静态资源缓存（便于开发）
- 更长的超时时间
- 支持热重载代理

### 3. 生产环境配置 (default.prod.conf)

**适用场景**: 生产部署
**特点**:
- 严格的安全头设置
- 长期静态资源缓存
- IP 访问控制支持
- 敏感文件访问禁止
- 请求方法限制

## 使用方法

### 1. 配置管理脚本

使用 `scripts/nginx-config.sh` 脚本管理配置：

```bash
# 查看帮助
./scripts/nginx-config.sh help

# 切换到开发环境配置
./scripts/nginx-config.sh switch dev

# 切换到生产环境配置
./scripts/nginx-config.sh switch prod

# 验证当前配置
./scripts/nginx-config.sh validate

# 备份当前配置
./scripts/nginx-config.sh backup

# 比较配置差异
./scripts/nginx-config.sh diff prod

# 重新加载配置（容器运行时）
./scripts/nginx-config.sh reload
```

### 2. 环境变量配置

在 `.env` 文件中设置：

```bash
# Nginx 配置环境 (default|dev|prod)
NGINX_CONFIG_ENV=dev

# 是否启用 Gzip 压缩
NGINX_GZIP_ENABLED=true

# 客户端最大请求体大小
NGINX_CLIENT_MAX_BODY_SIZE=50M
```

### 3. 自动配置

启动系统时会自动根据环境变量配置 Nginx：

```bash
# 启动时自动应用配置
./scripts/start.sh
```

## 配置详解

### 1. 安全配置

#### 开发环境
```nginx
# 相对宽松的安全头
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
```

#### 生产环境
```nginx
# 严格的安全头
add_header X-Frame-Options "DENY" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; ..." always;
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
```

### 2. 缓存策略

#### 开发环境
```nginx
# 禁用缓存，便于开发
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    expires -1;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
}
```

#### 生产环境
```nginx
# 长期缓存静态资源
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    gzip_static on;
}
```

### 3. 代理配置

所有环境都包含以下代理配置：

```nginx
# API 代理
location /api/ {
    proxy_pass http://backend:8088/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# Druid 监控代理
location /druid/ {
    proxy_pass http://backend:8088/druid/;
    # 生产环境可添加 IP 白名单
}
```

## 高级配置

### 1. 自定义配置

如需自定义配置，可以：

1. 复制现有配置文件
2. 修改配置内容
3. 使用脚本切换配置

```bash
# 复制生产配置为自定义配置
cp docker/nginx/conf.d/default.prod.conf docker/nginx/conf.d/default.custom.conf

# 编辑自定义配置
vim docker/nginx/conf.d/default.custom.conf

# 手动切换配置
cp docker/nginx/conf.d/default.custom.conf docker/nginx/conf.d/default.conf
```

### 2. SSL/HTTPS 配置

生产环境可添加 SSL 配置：

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # SSL 配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 其他配置...
}
```

### 3. 负载均衡配置

如需负载均衡，可配置 upstream：

```nginx
upstream backend_servers {
    server backend1:8088;
    server backend2:8088;
    server backend3:8088;
}

location /api/ {
    proxy_pass http://backend_servers/;
    # 其他代理配置...
}
```

## 故障排除

### 1. 配置验证失败

```bash
# 检查配置语法
./scripts/nginx-config.sh validate

# 查看详细错误信息
docker run --rm -v "$(pwd)/docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro" \
                 -v "$(pwd)/docker/nginx/conf.d/default.conf:/etc/nginx/conf.d/default.conf:ro" \
                 nginx:1.25-alpine nginx -t
```

### 2. 配置重载失败

```bash
# 检查容器状态
docker-compose ps frontend

# 查看容器日志
docker-compose logs frontend

# 手动重载配置
docker-compose exec frontend nginx -s reload
```

### 3. 代理连接失败

```bash
# 检查后端服务状态
docker-compose ps backend

# 测试后端连接
docker-compose exec frontend curl -f http://backend:8088/actuator/health
```

## 最佳实践

### 1. 配置管理

- 使用版本控制管理配置文件
- 定期备份重要配置
- 在切换配置前先验证语法
- 记录配置变更原因

### 2. 安全考虑

- 生产环境使用严格的安全配置
- 定期更新安全头设置
- 限制敏感端点的访问
- 启用访问日志监控

### 3. 性能优化

- 合理设置缓存策略
- 启用 Gzip 压缩
- 优化代理缓冲设置
- 监控响应时间

### 4. 监控和日志

- 配置适当的日志级别
- 定期检查错误日志
- 监控访问模式
- 设置告警机制

## 总结

新的 Nginx 配置管理方案提供了：

1. **灵活性**: 支持多环境配置切换
2. **可维护性**: 外部配置文件，无需重新构建镜像
3. **安全性**: 环境特定的安全配置
4. **便利性**: 自动化配置管理脚本
5. **可靠性**: 配置验证和备份机制

这种方案既保持了容器化部署的简洁性，又提供了生产环境所需的配置灵活性。
